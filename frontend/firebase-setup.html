<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Setup Helper - Drive On</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
        }
        .button {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Firebase Setup for Drive On</h1>
            <p>Follow these steps to get your Firebase Web App configuration</p>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> You need to complete these steps to enable Google Sign-In and real Firebase authentication.
        </div>

        <div class="step">
            <h3>Step 1: Open Firebase Console</h3>
            <p>Click the button below to open your Firebase project:</p>
            <a href="https://console.firebase.google.com/project/drive-on-b2af8/settings/general" class="button" target="_blank">
                Open Firebase Console
            </a>
        </div>

        <div class="step">
            <h3>Step 2: Create Web App (if not exists)</h3>
            <p>In the Firebase Console:</p>
            <ol>
                <li>Scroll down to <strong>"Your apps"</strong> section</li>
                <li>If you see a web app already, skip to Step 3</li>
                <li>If no web app exists, click <strong>"Add app"</strong></li>
                <li>Select the <strong>Web icon (&lt;/&gt;)</strong></li>
                <li>Enter app nickname: <code>Drive On Web</code></li>
                <li>Check <strong>"Also set up Firebase Hosting"</strong> (optional)</li>
                <li>Click <strong>"Register app"</strong></li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 3: Copy Firebase Configuration</h3>
            <p>You should see a configuration object like this:</p>
            <div class="code-block">
const firebaseConfig = {
  apiKey: "AIzaSyD...",
  authDomain: "drive-on-b2af8.firebaseapp.com",
  projectId: "drive-on-b2af8",
  storageBucket: "drive-on-b2af8.appspot.com",
  messagingSenderId: "107077033756",
  appId: "1:107077033756:web:..."
};
            </div>
            <p><strong>Copy the <code>apiKey</code> and <code>appId</code> values!</strong></p>
        </div>

        <div class="step">
            <h3>Step 4: Update Your Configuration</h3>
            <p>Open <code>frontend/src/config/firebase.ts</code> and replace:</p>
            <div class="code-block">
apiKey: "AIzaSyBYour-Web-API-Key", // Replace with your actual API key
appId: "1:107077033756:web:your-web-app-id" // Replace with your actual app ID
            </div>
        </div>

        <div class="step">
            <h3>Step 5: Enable Authentication</h3>
            <p>In Firebase Console:</p>
            <ol>
                <li>Go to <strong>Authentication</strong> → <strong>Sign-in method</strong></li>
                <li>Click on <strong>Google</strong> provider</li>
                <li>Toggle <strong>Enable</strong></li>
                <li>Add your email as a test user</li>
                <li>Click <strong>Save</strong></li>
            </ol>
            <a href="https://console.firebase.google.com/project/drive-on-b2af8/authentication/providers" class="button" target="_blank">
                Open Authentication Settings
            </a>
        </div>

        <div class="step">
            <h3>Step 6: Enable Firestore Database</h3>
            <p>In Firebase Console:</p>
            <ol>
                <li>Go to <strong>Firestore Database</strong></li>
                <li>Click <strong>Create database</strong></li>
                <li>Choose <strong>Start in test mode</strong></li>
                <li>Select location: <strong>us-central1</strong> (recommended)</li>
                <li>Click <strong>Done</strong></li>
            </ol>
            <a href="https://console.firebase.google.com/project/drive-on-b2af8/firestore" class="button" target="_blank">
                Open Firestore Settings
            </a>
        </div>

        <div class="step">
            <h3>Step 7: Enable Storage</h3>
            <p>In Firebase Console:</p>
            <ol>
                <li>Go to <strong>Storage</strong></li>
                <li>Click <strong>Get started</strong></li>
                <li>Choose <strong>Start in test mode</strong></li>
                <li>Select same location as Firestore</li>
                <li>Click <strong>Done</strong></li>
            </ol>
            <a href="https://console.firebase.google.com/project/drive-on-b2af8/storage" class="button" target="_blank">
                Open Storage Settings
            </a>
        </div>

        <div class="step">
            <h3>Step 8: Add Authorized Domains</h3>
            <p>In Firebase Console:</p>
            <ol>
                <li>Go to <strong>Authentication</strong> → <strong>Settings</strong> → <strong>Authorized domains</strong></li>
                <li>Add these domains:</li>
                <ul>
                    <li><code>localhost</code> (for development)</li>
                    <li><code>127.0.0.1</code> (for development)</li>
                    <li>Your production domain (when you deploy)</li>
                </ul>
            </ol>
        </div>

        <div class="success">
            <h3>✅ Once Complete, You'll Have:</h3>
            <ul>
                <li>✅ Google Sign-In working</li>
                <li>✅ Email/Password authentication</li>
                <li>✅ File uploads to Firebase Storage</li>
                <li>✅ Real-time Firestore database</li>
                <li>✅ Email verification</li>
                <li>✅ Password reset functionality</li>
            </ul>
        </div>

        <div class="highlight">
            <h3>🚀 Test Your Setup</h3>
            <p>After completing all steps:</p>
            <ol>
                <li>Save your changes to <code>firebase.ts</code></li>
                <li>Restart your development server: <code>npm run dev</code></li>
                <li>Try clicking "Sign in with Google" button</li>
                <li>You should see the Google account selection popup</li>
            </ol>
        </div>

        <div class="step">
            <h3>🔧 Troubleshooting</h3>
            <p><strong>If Google Sign-In doesn't work:</strong></p>
            <ul>
                <li>Check browser console for errors</li>
                <li>Ensure popup blockers are disabled</li>
                <li>Verify your apiKey and appId are correct</li>
                <li>Make sure Google provider is enabled in Firebase</li>
                <li>Check that localhost is in authorized domains</li>
            </ul>
        </div>

        <div class="warning">
            <strong>🔐 Security Note:</strong> Never commit your actual Firebase configuration to public repositories. Use environment variables for production deployments.
        </div>
    </div>
</body>
</html>
