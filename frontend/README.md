# Drive On - Frontend

A comprehensive React application for connecting drivers with job opportunities, built with modern web technologies and optimized for mobile deployment.

## 🚗 Features

- **Driver Marketplace**: Connect with verified professional drivers
- **Job Portal**: Post and find driving opportunities
- **Industry News**: Stay updated with automotive industry trends
- **Community Forum**: Connect with other drivers and share experiences
- **Dark/Light Mode**: Toggle between themes
- **Responsive Design**: Optimized for all screen sizes
- **Mobile-First**: Built for mobile deployment (Google Play Store)

## 🛠️ Tech Stack

- **React 19** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **Radix UI** for accessible components
- **Lucide React** for icons

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Install dependencies
```bash
npm install
```

2. Start the development server
```bash
npm run dev
```

3. Open [http://localhost:5173](http://localhost:5173) in your browser

## 📜 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 📱 Building for Mobile (AAB)

This project is designed to be packaged as an Android App Bundle (AAB) for Google Play Store deployment.

### Build Process

1. Build the production version:
```bash
npm run build
```

2. The built files will be in the `dist/` directory
3. Use a tool like Capacitor or Cordova to package as AAB:

```bash
# Example with Capacitor
npm install @capacitor/core @capacitor/cli
npx cap init
npx cap add android
npx cap copy
npx cap open android
```

## 📁 Project Structure

```
frontend/
├── src/
│   ├── components/
│   │   └── ui/           # Reusable UI components
│   ├── lib/
│   │   └── utils.ts      # Utility functions
│   ├── App.tsx           # Main application component
│   ├── main.tsx          # Application entry point
│   └── index.css         # Global styles
├── public/               # Static assets
├── tailwind.config.js    # Tailwind configuration
├── vite.config.ts        # Vite configuration
└── package.json          # Dependencies and scripts
```

## 🧩 Components

### UI Components
- **Button** - Customizable button component
- **Card** - Container component for content
- **Input/Textarea** - Form input components
- **Badge** - Status and category indicators
- **Avatar** - User profile images
- **Tabs** - Navigation tabs
- **Switch** - Toggle switches
- **Select** - Dropdown selections

### Main Sections
- **Home**: Hero section with features overview
- **Drivers**: Browse and search for drivers
- **Jobs**: Job listings and posting
- **News**: Industry news and updates
- **Forum**: Community messaging

## 📱 Mobile Optimization

- Responsive design with mobile-first approach
- Touch-friendly interface elements
- Optimized for various screen sizes
- PWA-ready for app-like experience
- Ready for Google Play Store deployment

## 🎨 Customization

### Themes
The app supports dark and light themes. Theme switching is handled via the Switch component in the navigation.

### Colors
Primary brand color is yellow (#EAB308). Customize colors in `tailwind.config.js`.

## 🚀 Deployment

### Web Deployment
```bash
npm run build
# Deploy dist/ folder to your hosting service
```

### Mobile App Deployment
1. Build the web app
2. Use Capacitor/Cordova to create native wrapper
3. Generate AAB file for Google Play Store
4. Follow Google Play Console guidelines for submission

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
