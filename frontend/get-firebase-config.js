/**
 * Firebase Configuration Helper
 * This script helps you get the Firebase Web App configuration
 */

import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Your Firebase project details from the service account
const PROJECT_ID = 'drive-on-b2af8';
const SERVICE_ACCOUNT_PATH = '../backend/drive-on-b2af8-firebase-adminsdk-fbsvc-05c376e78b.json';

console.log('🔥 Firebase Configuration Helper for Drive On');
console.log('='.repeat(50));

// Read service account file
try {
  const serviceAccount = JSON.parse(fs.readFileSync(path.join(__dirname, SERVICE_ACCOUNT_PATH), 'utf8'));
  
  console.log('✅ Service Account Details:');
  console.log(`   Project ID: ${serviceAccount.project_id}`);
  console.log(`   Client ID: ${serviceAccount.client_id}`);
  console.log(`   Client Email: ${serviceAccount.client_email}`);
  console.log('');
  
  // Generate the Firebase config template
  const firebaseConfig = {
    apiKey: "YOUR_WEB_API_KEY", // You need to get this from Firebase Console
    authDomain: `${serviceAccount.project_id}.firebaseapp.com`,
    projectId: serviceAccount.project_id,
    storageBucket: `${serviceAccount.project_id}.appspot.com`,
    messagingSenderId: serviceAccount.client_id.split('').slice(0, 12).join(''), // Extract from client_id
    appId: "YOUR_WEB_APP_ID" // You need to get this from Firebase Console
  };
  
  console.log('🔧 Firebase Web Configuration Template:');
  console.log('Copy this to frontend/src/config/firebase.ts:');
  console.log('');
  console.log('const firebaseConfig = {');
  console.log(`  apiKey: "${firebaseConfig.apiKey}",`);
  console.log(`  authDomain: "${firebaseConfig.authDomain}",`);
  console.log(`  projectId: "${firebaseConfig.projectId}",`);
  console.log(`  storageBucket: "${firebaseConfig.storageBucket}",`);
  console.log(`  messagingSenderId: "${firebaseConfig.messagingSenderId}",`);
  console.log(`  appId: "${firebaseConfig.appId}"`);
  console.log('};');
  console.log('');
  
  console.log('📋 Steps to get missing values:');
  console.log('');
  console.log('1. Go to Firebase Console: https://console.firebase.google.com/');
  console.log(`2. Select your project: ${serviceAccount.project_id}`);
  console.log('3. Click Settings (⚙️) > Project settings');
  console.log('4. Scroll to "Your apps" section');
  console.log('5. If no web app exists:');
  console.log('   - Click "Add app" > Web (</> icon)');
  console.log('   - Enter nickname: "Drive On Web"');
  console.log('   - Click "Register app"');
  console.log('6. Copy the apiKey and appId from the config object');
  console.log('');
  
  console.log('🔐 Enable Authentication:');
  console.log('1. Go to Authentication > Sign-in method');
  console.log('2. Enable Google provider');
  console.log('3. Add authorized domains: localhost, your-domain.com');
  console.log('');
  
  console.log('💾 Enable Storage:');
  console.log('1. Go to Storage > Get started');
  console.log('2. Choose "Start in test mode" for now');
  console.log('3. Select a location (us-central1 recommended)');
  console.log('');
  
  console.log('🔥 Enable Firestore:');
  console.log('1. Go to Firestore Database > Create database');
  console.log('2. Choose "Start in test mode"');
  console.log('3. Select same location as Storage');
  console.log('');
  
  // Try to update the Firebase config file automatically
  const configPath = path.join(__dirname, 'src/config/firebase.ts');
  if (fs.existsSync(configPath)) {
    try {
      let configContent = fs.readFileSync(configPath, 'utf8');
      
      // Update the config with known values
      configContent = configContent.replace(
        /messagingSenderId: "[^"]*"/,
        `messagingSenderId: "${firebaseConfig.messagingSenderId}"`
      );
      
      fs.writeFileSync(configPath, configContent);
      console.log('✅ Updated messagingSenderId in firebase.ts');
    } catch (error) {
      console.log('⚠️  Could not auto-update firebase.ts:', error.message);
    }
  }
  
} catch (error) {
  console.error('❌ Error reading service account file:', error.message);
  console.log('');
  console.log('Make sure the service account file exists at:');
  console.log(path.join(__dirname, SERVICE_ACCOUNT_PATH));
}

console.log('');
console.log('🚀 Once configured, your app will have:');
console.log('   ✅ Google Sign-In');
console.log('   ✅ Email/Password Authentication');
console.log('   ✅ File Upload to Firebase Storage');
console.log('   ✅ Real-time Firestore Database');
console.log('   ✅ Email Verification');
console.log('   ✅ Password Reset');
console.log('');
console.log('Run this script anytime with: node get-firebase-config.js');
