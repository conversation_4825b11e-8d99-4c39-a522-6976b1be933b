/**
 * Real Firebase Service for Drive On
 * Uses actual Firebase SDK with Google Sign-In
 */

import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  onAuthStateChanged,
  GoogleAuthProvider,
  type User as FirebaseUser
} from 'firebase/auth';
import { auth, googleProvider } from '../config/firebase';
import { checkFirebaseConfig } from '../utils/firebase-checker';

export interface User {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

interface AuthState {
  user: User | null;
  loading: boolean;
}

type AuthListener = (user: User | null) => void;

// Convert Firebase User to our User interface
const convertFirebaseUser = (firebaseUser: FirebaseUser | null): User | null => {
  if (!firebaseUser) return null;
  
  return {
    uid: firebaseUser.uid,
    email: firebaseUser.email,
    displayName: firebaseUser.displayName,
    photoURL: firebaseUser.photoURL,
    emailVerified: firebaseUser.emailVerified
  };
};

class RealFirebaseService {
  private authState: AuthState = { user: null, loading: true };
  private listeners: AuthListener[] = [];
  private unsubscribeAuth: (() => void) | null = null;

  constructor() {
    this.initializeAuth();
  }

  private initializeAuth() {
    // Listen to Firebase auth state changes
    this.unsubscribeAuth = onAuthStateChanged(auth, (firebaseUser) => {
      const user = convertFirebaseUser(firebaseUser);
      this.authState = { user, loading: false };
      this.notifyListeners(user);
    });
  }

  private notifyListeners(user: User | null) {
    this.listeners.forEach(listener => listener(user));
  }

  onAuthStateChanged(callback: AuthListener): () => void {
    this.listeners.push(callback);
    
    // Immediately call with current user if not loading
    if (!this.authState.loading) {
      callback(this.authState.user);
    }
    
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  async signInWithEmail(email: string, password: string): Promise<User> {
    try {
      const result = await signInWithEmailAndPassword(auth, email, password);
      const user = convertFirebaseUser(result.user);
      if (!user) throw new Error('Failed to sign in');
      return user;
    } catch (error: any) {
      console.error('Email sign in error:', error);
      throw new Error(this.getErrorMessage(error.code) || 'Sign in failed');
    }
  }

  async signInWithGoogle(): Promise<User> {
    try {
      console.log('Starting Google sign-in...');
      
      // Configure Google provider
      googleProvider.setCustomParameters({
        prompt: 'select_account'
      });
      
      const result = await signInWithPopup(auth, googleProvider);
      console.log('Google sign-in successful:', result.user);
      
      const user = convertFirebaseUser(result.user);
      if (!user) throw new Error('Failed to get user data from Google sign-in');
      
      return user;
    } catch (error: any) {
      console.error('Google sign in error:', error);
      
      // Handle specific Google sign-in errors
      if (error.code === 'auth/popup-closed-by-user') {
        throw new Error('Sign-in was cancelled');
      } else if (error.code === 'auth/popup-blocked') {
        throw new Error('Popup was blocked by browser. Please allow popups and try again.');
      } else if (error.code === 'auth/cancelled-popup-request') {
        throw new Error('Another sign-in popup is already open');
      }
      
      throw new Error(this.getErrorMessage(error.code) || 'Google sign-in failed');
    }
  }

  async signUpWithEmail(email: string, password: string): Promise<User> {
    try {
      const result = await createUserWithEmailAndPassword(auth, email, password);
      const user = convertFirebaseUser(result.user);
      if (!user) throw new Error('Failed to create user');
      return user;
    } catch (error: any) {
      console.error('Email sign up error:', error);
      throw new Error(this.getErrorMessage(error.code) || 'Sign up failed');
    }
  }

  async signOut(): Promise<void> {
    try {
      await firebaseSignOut(auth);
    } catch (error: any) {
      console.error('Sign out error:', error);
      throw new Error('Sign out failed');
    }
  }

  async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: any) {
      console.error('Password reset error:', error);
      throw new Error(this.getErrorMessage(error.code) || 'Password reset failed');
    }
  }

  async getIdToken(): Promise<string> {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No user signed in');
      }
      return await user.getIdToken();
    } catch (error: any) {
      console.error('Get ID token error:', error);
      throw new Error('Failed to get authentication token');
    }
  }

  getCurrentUser(): User | null {
    return convertFirebaseUser(auth.currentUser);
  }

  getAuthState(): AuthState {
    return this.authState;
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser();
  }

  // Helper method to get user-friendly error messages
  private getErrorMessage(errorCode: string): string {
    switch (errorCode) {
      case 'auth/user-not-found':
        return 'No account found with this email address';
      case 'auth/wrong-password':
        return 'Incorrect password';
      case 'auth/email-already-in-use':
        return 'An account with this email already exists';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters';
      case 'auth/invalid-email':
        return 'Invalid email address';
      case 'auth/user-disabled':
        return 'This account has been disabled';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later';
      case 'auth/network-request-failed':
        return 'Network error. Please check your connection';
      default:
        return 'An error occurred. Please try again';
    }
  }

  // Cleanup method
  destroy() {
    if (this.unsubscribeAuth) {
      this.unsubscribeAuth();
    }
    this.listeners = [];
  }
}

export const realFirebaseService = new RealFirebaseService();

// Utility functions
export const formatAuthError = (error: any): string => {
  const errorMessages: { [key: string]: string } = {
    'auth/user-not-found': 'No account found with this email address.',
    'auth/wrong-password': 'Incorrect password. Please try again.',
    'auth/email-already-in-use': 'An account with this email already exists.',
    'auth/weak-password': 'Password should be at least 6 characters long.',
    'auth/invalid-email': 'Please enter a valid email address.',
    'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
    'auth/network-request-failed': 'Network error. Please check your connection.',
  };

  return errorMessages[error.code] || error.message || 'An unexpected error occurred.';
};
