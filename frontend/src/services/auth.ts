/**
 * Firebase Authentication Service for Drive On
 * Handles Google sign-in, email registration, and email verification
 */

import { APIClient } from './api';

export interface User {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export interface UserProfile {
  firebase_uid: string;
  email: string;
  full_name?: string;
  user_type: 'driver' | 'employer';
  is_active: boolean;
  is_verified: boolean;
  email_verified: boolean;
  profile_image_url?: string;
  created_at?: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    profile?: UserProfile;
    verification_link?: string;
    email_sent?: boolean;
  };
}

class AuthService {
  private apiClient: APIClient;

  constructor() {
    this.apiClient = new APIClient();
  }

  /**
   * Register with email and password
   */
  async registerWithEmail(
    email: string, 
    password: string, 
    fullName?: string, 
    userType: 'driver' | 'employer' = 'driver'
  ): Promise<AuthResponse> {
    try {
      const response = await this.apiClient.post<AuthResponse['data']>('/api/v1/auth-firebase/register-email', {
        email,
        password,
        full_name: fullName,
        user_type: userType
      });

      return {
        success: true,
        message: response.message || 'Registration successful',
        data: response.data
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Registration failed');
    }
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(email: string): Promise<{ success: boolean; message: string; verification_link?: string }> {
    try {
      const response = await this.apiClient.post('/api/v1/auth-firebase/send-email-verification', {
        email
      });

      return {
        success: true,
        message: response.message || 'Verification email sent',
        verification_link: response.data?.verification_link
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to send verification email');
    }
  }

  /**
   * Verify email with code
   */
  async verifyEmail(oobCode: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.apiClient.post('/api/v1/auth-firebase/verify-email', {
        oob_code: oobCode
      });

      return {
        success: true,
        message: response.message || 'Email verified successfully'
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Email verification failed');
    }
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(email: string): Promise<{ success: boolean; message: string; reset_link?: string }> {
    try {
      const response = await this.apiClient.post('/api/v1/auth-firebase/send-password-reset', {
        email
      });

      return {
        success: true,
        message: response.message || 'Password reset email sent',
        reset_link: response.data?.reset_link
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to send password reset email');
    }
  }

  /**
   * Sign in with Google (requires Google ID token from client)
   */
  async signInWithGoogle(idToken: string): Promise<AuthResponse> {
    try {
      const response = await this.apiClient.post<AuthResponse['data']>('/api/v1/auth-firebase/google-signin', {
        id_token: idToken
      });

      return {
        success: true,
        message: response.message || 'Google sign-in successful',
        data: response.data
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Google sign-in failed');
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<{ user: User; profile: UserProfile }> {
    try {
      const response = await this.apiClient.get('/api/v1/auth-firebase/me');
      return {
        user: response.data.firebase_user,
        profile: response.data.profile
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to get user profile');
    }
  }

  /**
   * Check email verification status
   */
  async checkEmailVerified(email: string): Promise<{ isVerified: boolean; uid: string }> {
    try {
      const response = await this.apiClient.get(`/api/v1/auth-firebase/check-email-verified/${encodeURIComponent(email)}`);
      return {
        isVerified: response.data.is_verified,
        uid: response.data.uid
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.detail || 'Failed to check email verification');
    }
  }

  /**
   * Initialize Google Sign-In (client-side)
   * This should be called in your React component
   */
  initializeGoogleSignIn(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Load Google Sign-In script
      if (typeof window !== 'undefined' && !window.google) {
        const script = document.createElement('script');
        script.src = 'https://accounts.google.com/gsi/client';
        script.onload = () => {
          // Initialize Google Sign-In
          window.google?.accounts.id.initialize({
            client_id: 'YOUR_GOOGLE_CLIENT_ID', // Replace with your Google Client ID
            callback: this.handleGoogleSignIn.bind(this)
          });
          resolve();
        };
        script.onerror = () => reject(new Error('Failed to load Google Sign-In'));
        document.head.appendChild(script);
      } else {
        resolve();
      }
    });
  }

  /**
   * Handle Google Sign-In callback
   */
  private async handleGoogleSignIn(response: any) {
    try {
      const result = await this.signInWithGoogle(response.credential);
      // Handle successful sign-in
      console.log('Google sign-in successful:', result);
      
      // You can emit an event or call a callback here
      window.dispatchEvent(new CustomEvent('googleSignInSuccess', { detail: result }));
    } catch (error) {
      console.error('Google sign-in error:', error);
      window.dispatchEvent(new CustomEvent('googleSignInError', { detail: error }));
    }
  }

  /**
   * Render Google Sign-In button
   */
  renderGoogleSignInButton(elementId: string, theme: 'outline' | 'filled_blue' = 'outline') {
    if (typeof window !== 'undefined' && window.google) {
      window.google.accounts.id.renderButton(
        document.getElementById(elementId),
        {
          theme,
          size: 'large',
          width: 300,
          text: 'signin_with'
        }
      );
    }
  }

  /**
   * Extract verification code from URL (for email verification)
   */
  getVerificationCodeFromUrl(): string | null {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('oobCode');
    }
    return null;
  }

  /**
   * Extract email from URL (for email verification)
   */
  getEmailFromUrl(): string | null {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('email');
    }
    return null;
  }
}

// Export singleton instance
export const authService = new AuthService();

// Type declarations for Google Sign-In
declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          renderButton: (element: HTMLElement | null, config: any) => void;
          prompt: () => void;
        };
      };
    };
  }
}
