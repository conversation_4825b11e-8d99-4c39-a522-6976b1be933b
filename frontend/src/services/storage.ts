/**
 * Firebase Storage Service for Drive On
 * Handles file uploads, image storage, and document management
 */

import { 
  ref, 
  uploadBytes, 
  uploadBytesResumable, 
  getDownloadURL, 
  deleteObject,
  listAll,
  getMetadata
} from 'firebase/storage';
import { storage } from '../config/firebase';

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  progress: number;
}

export interface UploadResult {
  url: string;
  path: string;
  name: string;
  size: number;
  contentType: string;
}

class StorageService {
  
  /**
   * Upload a file to Firebase Storage
   */
  async uploadFile(
    file: File, 
    path: string, 
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      const storageRef = ref(storage, path);
      
      if (onProgress) {
        // Upload with progress tracking
        const uploadTask = uploadBytesResumable(storageRef, file);
        
        return new Promise((resolve, reject) => {
          uploadTask.on(
            'state_changed',
            (snapshot) => {
              const progress = {
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
                progress: (snapshot.bytesTransferred / snapshot.totalBytes) * 100
              };
              onProgress(progress);
            },
            (error) => {
              console.error('Upload error:', error);
              reject(new Error(`Upload failed: ${error.message}`));
            },
            async () => {
              try {
                const url = await getDownloadURL(uploadTask.snapshot.ref);
                const metadata = await getMetadata(uploadTask.snapshot.ref);
                
                resolve({
                  url,
                  path,
                  name: file.name,
                  size: metadata.size,
                  contentType: metadata.contentType || file.type
                });
              } catch (error) {
                reject(error);
              }
            }
          );
        });
      } else {
        // Simple upload without progress
        const snapshot = await uploadBytes(storageRef, file);
        const url = await getDownloadURL(snapshot.ref);
        const metadata = await getMetadata(snapshot.ref);
        
        return {
          url,
          path,
          name: file.name,
          size: metadata.size,
          contentType: metadata.contentType || file.type
        };
      }
    } catch (error: any) {
      console.error('File upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Upload profile image
   */
  async uploadProfileImage(file: File, userId: string, onProgress?: (progress: UploadProgress) => void): Promise<UploadResult> {
    const path = `profiles/${userId}/avatar_${Date.now()}_${file.name}`;
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload driver documents (CNIC, License, etc.)
   */
  async uploadDriverDocument(
    file: File, 
    userId: string, 
    documentType: 'cnic_front' | 'cnic_back' | 'license' | 'electricity_bill' | 'police_certificate',
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    const path = `drivers/${userId}/documents/${documentType}_${Date.now()}_${file.name}`;
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload job-related images
   */
  async uploadJobImage(file: File, jobId: string, onProgress?: (progress: UploadProgress) => void): Promise<UploadResult> {
    const path = `jobs/${jobId}/images/${Date.now()}_${file.name}`;
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload news article images
   */
  async uploadNewsImage(file: File, articleId: string, onProgress?: (progress: UploadProgress) => void): Promise<UploadResult> {
    const path = `news/${articleId}/images/${Date.now()}_${file.name}`;
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Upload forum message attachments
   */
  async uploadForumAttachment(file: File, forumId: string, messageId: string, onProgress?: (progress: UploadProgress) => void): Promise<UploadResult> {
    const path = `forums/${forumId}/messages/${messageId}/attachments/${Date.now()}_${file.name}`;
    return this.uploadFile(file, path, onProgress);
  }

  /**
   * Delete a file from Firebase Storage
   */
  async deleteFile(path: string): Promise<void> {
    try {
      const storageRef = ref(storage, path);
      await deleteObject(storageRef);
    } catch (error: any) {
      console.error('File deletion error:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * Get download URL for a file
   */
  async getDownloadURL(path: string): Promise<string> {
    try {
      const storageRef = ref(storage, path);
      return await getDownloadURL(storageRef);
    } catch (error: any) {
      console.error('Get download URL error:', error);
      throw new Error(`Failed to get download URL: ${error.message}`);
    }
  }

  /**
   * List files in a directory
   */
  async listFiles(path: string): Promise<string[]> {
    try {
      const storageRef = ref(storage, path);
      const result = await listAll(storageRef);
      
      const urls = await Promise.all(
        result.items.map(async (item) => {
          return await getDownloadURL(item);
        })
      );
      
      return urls;
    } catch (error: any) {
      console.error('List files error:', error);
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File, options: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
    maxWidth?: number;
    maxHeight?: number;
  } = {}): Promise<boolean> {
    return new Promise((resolve, reject) => {
      const {
        maxSize = 5 * 1024 * 1024, // 5MB default
        allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        maxWidth = 2000,
        maxHeight = 2000
      } = options;

      // Check file size
      if (file.size > maxSize) {
        reject(new Error(`File size too large. Maximum size is ${maxSize / 1024 / 1024}MB`));
        return;
      }

      // Check file type
      if (!allowedTypes.includes(file.type)) {
        reject(new Error(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`));
        return;
      }

      // For images, check dimensions
      if (file.type.startsWith('image/')) {
        const img = new Image();
        img.onload = () => {
          if (img.width > maxWidth || img.height > maxHeight) {
            reject(new Error(`Image dimensions too large. Maximum: ${maxWidth}x${maxHeight}px`));
          } else {
            resolve(true);
          }
        };
        img.onerror = () => reject(new Error('Invalid image file'));
        img.src = URL.createObjectURL(file);
      } else {
        resolve(true);
      }
    });
  }

  /**
   * Resize image before upload (client-side)
   */
  async resizeImage(file: File, maxWidth: number = 800, maxHeight: number = 600, quality: number = 0.8): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now()
              });
              resolve(resizedFile);
            } else {
              reject(new Error('Failed to resize image'));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }
}

// Export singleton instance
export const storageService = new StorageService();
export default storageService;
