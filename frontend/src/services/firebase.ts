/**
 * Firebase Authentication Service
 * Handles Firebase auth integration for Drive On
 */

// Mock Firebase implementation for development
// In production, replace with actual Firebase SDK

export interface User {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
}

export interface AuthError {
  code: string;
  message: string;
}

class MockFirebaseAuth {
  private currentUser: User | null = null;
  private listeners: ((user: User | null) => void)[] = [];

  // Mock user for development
  private mockUser: User = {
    uid: 'mock-user-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    photoURL: null,
    emailVerified: true,
  };

  async signInWithEmailAndPassword(email: string, password: string): Promise<{ user: User }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simple validation for demo
    if (email && password.length >= 6) {
      this.currentUser = {
        ...this.mockUser,
        email,
        displayName: email.split('@')[0],
      };
      
      this.notifyListeners();
      return { user: this.currentUser };
    } else {
      throw new Error('Invalid email or password');
    }
  }

  async signInWithPopup(): Promise<{ user: User }> {
    // Simulate Google sign-in
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    this.currentUser = {
      ...this.mockUser,
      displayName: 'Google User',
      email: '<EMAIL>',
    };
    
    this.notifyListeners();
    return { user: this.currentUser };
  }

  async createUserWithEmailAndPassword(email: string, password: string): Promise<{ user: User }> {
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (email && password.length >= 6) {
      this.currentUser = {
        ...this.mockUser,
        email,
        displayName: email.split('@')[0],
        emailVerified: false,
      };
      
      this.notifyListeners();
      return { user: this.currentUser };
    } else {
      throw new Error('Invalid email or password');
    }
  }

  async signOut(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 500));
    this.currentUser = null;
    this.notifyListeners();
  }

  async sendPasswordResetEmail(email: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log(`Password reset email sent to: ${email}`);
  }

  async getIdToken(): Promise<string> {
    if (!this.currentUser) {
      throw new Error('No user signed in');
    }
    
    // Return a mock JWT token for development
    return `mock-firebase-token-${this.currentUser.uid}-${Date.now()}`;
  }

  getCurrentUser(): User | null {
    return this.currentUser;
  }

  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    this.listeners.push(callback);
    
    // Call immediately with current state
    callback(this.currentUser);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.currentUser));
  }
}

// Firebase service instance
class FirebaseService {
  private auth: MockFirebaseAuth;

  constructor() {
    this.auth = new MockFirebaseAuth();
  }

  // Authentication methods
  async signInWithEmail(email: string, password: string): Promise<User> {
    try {
      const result = await this.auth.signInWithEmailAndPassword(email, password);
      return result.user;
    } catch (error: any) {
      throw new Error(error.message || 'Sign in failed');
    }
  }

  async signInWithGoogle(): Promise<User> {
    try {
      const result = await this.auth.signInWithPopup();
      return result.user;
    } catch (error: any) {
      throw new Error(error.message || 'Google sign in failed');
    }
  }

  async signUpWithEmail(email: string, password: string): Promise<User> {
    try {
      const result = await this.auth.createUserWithEmailAndPassword(email, password);
      return result.user;
    } catch (error: any) {
      throw new Error(error.message || 'Sign up failed');
    }
  }

  async signOut(): Promise<void> {
    try {
      await this.auth.signOut();
    } catch (error: any) {
      throw new Error(error.message || 'Sign out failed');
    }
  }

  async resetPassword(email: string): Promise<void> {
    try {
      await this.auth.sendPasswordResetEmail(email);
    } catch (error: any) {
      throw new Error(error.message || 'Password reset failed');
    }
  }

  async getIdToken(): Promise<string> {
    try {
      return await this.auth.getIdToken();
    } catch (error: any) {
      throw new Error(error.message || 'Failed to get ID token');
    }
  }

  getCurrentUser(): User | null {
    return this.auth.getCurrentUser();
  }

  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    return this.auth.onAuthStateChanged(callback);
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser();
  }
}

// Export singleton instance
export const firebaseService = new FirebaseService();
export default firebaseService;

// Utility functions
export const formatAuthError = (error: any): string => {
  const errorMessages: { [key: string]: string } = {
    'auth/user-not-found': 'No account found with this email address.',
    'auth/wrong-password': 'Incorrect password. Please try again.',
    'auth/email-already-in-use': 'An account with this email already exists.',
    'auth/weak-password': 'Password should be at least 6 characters long.',
    'auth/invalid-email': 'Please enter a valid email address.',
    'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
    'auth/network-request-failed': 'Network error. Please check your connection.',
  };

  return errorMessages[error.code] || error.message || 'An unexpected error occurred.';
};
