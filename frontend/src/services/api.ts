/**
 * API Service for Drive On Frontend
 * Handles all backend communication with proper error handling and authentication
 */

// API Configuration
const API_BASE_URL = 'http://localhost:8000';

// Types
export interface APIResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface Driver {
  id: string;
  name: string;
  mobile_number: string;
  education: string;
  experience_years: number;
  city: string;
  rating?: number;
  verified: boolean;
  created_at: string;
}

export interface Job {
  id: string;
  title: string;
  description: string;
  category: 'household' | 'big_vehicles' | 'online_service' | 'company';
  employment_type: 'part_time' | 'full_time';
  package_amount: number;
  city: string;
  mobile_number: string;
  contact_email: string;
  benefits: {
    residency: boolean;
    food: boolean;
    medical_insurance: boolean;
  };
  vehicle_provided: boolean;
  created_at: string;
  employer_name?: string;
}

export interface NewsArticle {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  image_url?: string;
  source: string;
  category: string;
  published_at: string;
  url: string;
}

export interface ForumMessage {
  id: string;
  user_name: string;
  content: string;
  message_type: 'text' | 'voice';
  voice_duration?: number;
  created_at: string;
  city: string;
}

// Authentication token management
class AuthManager {
  private static token: string | null = null;

  static setToken(token: string) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  static getToken(): string | null {
    if (!this.token) {
      this.token = localStorage.getItem('auth_token');
    }
    return this.token;
  }

  static clearToken() {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  static isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

// HTTP Client
class APIClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add authentication header if token exists
    const token = AuthManager.getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.detail || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  async get<T>(endpoint: string): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// API Service
class APIService {
  private client: APIClient;

  constructor() {
    this.client = new APIClient();
  }

  // Authentication
  async verifyToken(token: string): Promise<APIResponse<any>> {
    return this.client.post('/api/v1/auth/verify-token', { token });
  }

  async login(firebaseToken: string): Promise<APIResponse<any>> {
    const response = await this.client.post('/api/v1/auth/login', {}, {
      headers: { Authorization: `Bearer ${firebaseToken}` }
    });
    
    if (response.success) {
      AuthManager.setToken(firebaseToken);
    }
    
    return response;
  }

  async getCurrentUser(): Promise<APIResponse<any>> {
    return this.client.get('/api/v1/auth/me');
  }

  // Drivers
  async getDrivers(params?: {
    city?: string;
    education?: string;
    experience_min?: number;
    verified_only?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<APIResponse<Driver[]>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/api/v1/firestore/drivers/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return this.client.get<Driver[]>(endpoint);
  }

  async searchDrivers(query: string): Promise<APIResponse<Driver[]>> {
    return this.client.get<Driver[]>(`/api/v1/drivers/search?q=${encodeURIComponent(query)}`);
  }

  async createDriver(driverData: Partial<Driver>): Promise<APIResponse<Driver>> {
    return this.client.post<Driver>('/api/v1/firestore/drivers/', driverData);
  }

  // Jobs
  async getJobs(params?: {
    category?: string;
    employment_type?: string;
    city?: string;
    package_min?: number;
    package_max?: number;
    limit?: number;
    offset?: number;
  }): Promise<APIResponse<Job[]>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/api/v1/firestore/jobs/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return this.client.get<Job[]>(endpoint);
  }

  async createJob(jobData: Partial<Job>): Promise<APIResponse<Job>> {
    return this.client.post<Job>('/api/v1/firestore/jobs/', jobData);
  }

  async getJob(jobId: string): Promise<APIResponse<Job>> {
    return this.client.get<Job>(`/api/v1/jobs/${jobId}`);
  }

  // News
  async getNews(params?: {
    category?: string;
    source?: string;
    limit?: number;
    offset?: number;
  }): Promise<APIResponse<NewsArticle[]>> {
    const queryParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
    }
    
    const endpoint = `/api/v1/firestore/news/${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    return this.client.get<NewsArticle[]>(endpoint);
  }

  async getNewsCategories(): Promise<APIResponse<string[]>> {
    return this.client.get<string[]>('/api/v1/news/categories/');
  }

  async getNewsSources(): Promise<APIResponse<string[]>> {
    return this.client.get<string[]>('/api/v1/news/sources/');
  }

  // Forum (using Firestore)
  async getForums(): Promise<APIResponse<any[]>> {
    return this.client.get<any[]>('/api/v1/firestore/forums/');
  }

  async getForumMessages(forumId: string = '1', city?: string): Promise<APIResponse<ForumMessage[]>> {
    const endpoint = `/api/v1/firestore/forums/${forumId}/messages`;
    return this.client.get<ForumMessage[]>(endpoint);
  }

  async sendForumMessage(data: {
    content: string;
    message_type: 'text' | 'voice';
    city: string;
    voice_duration?: number;
  }): Promise<APIResponse<ForumMessage>> {
    // For now, use forum ID 1 (general forum)
    return this.client.post<ForumMessage>('/api/v1/firestore/forums/1/messages', {
      message_text: data.content,
      message_type: data.message_type,
      voice_duration: data.voice_duration
    });
  }
}

// Export singleton instance
export const apiService = new APIService();
export { AuthManager };
export default apiService;
