/**
 * Driver Registration Form Component
 * Comprehensive driver registration with all required fields and document uploads
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Phone, MapPin, GraduationCap, Calendar, AlertCircle, CheckCircle, Upload, FileText, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface DriverFormData {
  name: string;
  father_name: string;
  mobile_number: string;
  education: 'primary' | 'secondary' | 'intermediate' | 'bachelor' | 'master' | 'phd' | 'diploma' | 'certificate' | '';
  experience_years: string;
  marital_status: 'single' | 'married' | 'divorced' | 'widowed' | '';
  city_of_priority: string;
  bio: string;
  // Document URLs (will be set after upload)
  profile_picture_url: string;
  cnic_front_url: string;
  cnic_back_url: string;
  driving_license_url: string;
  electricity_bill_url: string;
  police_certificate_url: string;
}

interface DriverRegistrationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const DriverRegistrationForm: React.FC<DriverRegistrationFormProps> = ({ onSuccess, onCancel }) => {
  const { user, isAuthenticated } = useAuth();
  const [formData, setFormData] = useState<DriverFormData>({
    name: user?.displayName || '',
    father_name: '',
    mobile_number: '',
    education: '',
    experience_years: '',
    marital_status: '',
    city_of_priority: '',
    bio: '',
    profile_picture_url: '',
    cnic_front_url: '',
    cnic_back_url: '',
    driving_license_url: '',
    electricity_bill_url: '',
    police_certificate_url: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [uploadingDocument, setUploadingDocument] = useState<string | null>(null);

  const handleInputChange = (field: keyof DriverFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): string | null => {
    // Personal Information
    if (!formData.name.trim()) return 'Full name is required';
    if (!formData.father_name.trim()) return 'Father\'s name is required';
    if (!formData.mobile_number.trim()) return 'Mobile number is required';
    if (!formData.education) return 'Education level is required';
    if (!formData.experience_years || isNaN(Number(formData.experience_years))) return 'Valid experience years is required';
    if (!formData.marital_status) return 'Marital status is required';
    if (!formData.city_of_priority.trim()) return 'City of priority is required';

    // Validate mobile number format
    const mobileRegex = /^\+?[1-9]\d{1,14}$/;
    if (!mobileRegex.test(formData.mobile_number.replace(/\s/g, ''))) {
      return 'Please enter a valid mobile number with country code (e.g., +923001234567)';
    }

    const experience = Number(formData.experience_years);
    if (experience < 0 || experience > 50) {
      return 'Experience years must be between 0 and 50';
    }

    // Required Documents
    if (!formData.cnic_front_url) return 'CNIC front image is required';
    if (!formData.cnic_back_url) return 'CNIC back image is required';
    if (!formData.driving_license_url) return 'Driving license image is required';
    if (!formData.electricity_bill_url) return 'Electricity bill image is required';
    if (!formData.police_certificate_url) return 'Police certificate is required';

    return null;
  };

  const handleFileUpload = async (file: File, documentType: string): Promise<string> => {
    setUploadingDocument(documentType);
    try {
      // This would typically upload to Firebase Storage or similar
      // For now, we'll simulate the upload and return a placeholder URL
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate upload delay
      const mockUrl = `https://storage.googleapis.com/drive-on-docs/${documentType}_${Date.now()}.jpg`;
      return mockUrl;
    } catch (error) {
      throw new Error(`Failed to upload ${documentType}`);
    } finally {
      setUploadingDocument(null);
    }
  };

  const handleDocumentUpload = async (e: React.ChangeEvent<HTMLInputElement>, documentType: keyof DriverFormData) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
      setError('Please upload only JPG, PNG, or PDF files');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB');
      return;
    }

    try {
      const url = await handleFileUpload(file, documentType);
      setFormData(prev => ({
        ...prev,
        [documentType]: url
      }));
      setError(null);
    } catch (error: any) {
      setError(error.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated) {
      setError('Please login to register as a driver');
      return;
    }

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const driverData = {
        name: formData.name,
        father_name: formData.father_name,
        mobile_number: formData.mobile_number,
        education: formData.education,
        experience_years: Number(formData.experience_years),
        marital_status: formData.marital_status,
        city_of_priority: formData.city_of_priority,
        bio: formData.bio || undefined,
        profile_picture_url: formData.profile_picture_url || undefined,
        cnic_front_url: formData.cnic_front_url,
        cnic_back_url: formData.cnic_back_url,
        driving_license_url: formData.driving_license_url,
        electricity_bill_url: formData.electricity_bill_url,
        police_certificate_url: formData.police_certificate_url,
      };

      const response = await apiService.registerDriver(driverData);
      
      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          onSuccess?.();
        }, 2000);
      } else {
        setError(response.error || 'Failed to register as driver');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to register as driver');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center p-8 text-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring" }}
          className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mb-4"
        >
          <CheckCircle className="w-8 h-8 text-black" />
        </motion.div>
        <h3 className="text-xl font-bold text-foreground mb-2">Registration Successful!</h3>
        <p className="text-muted-foreground mb-4">Your driver profile has been created successfully.</p>
        <Button 
          onClick={onSuccess}
          className="bg-yellow-500 hover:bg-yellow-600 text-black"
        >
          View Driver Profiles
        </Button>
      </motion.div>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto border-0 bg-background/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-foreground">
          <User className="h-5 w-5 text-yellow-500" />
          Register as Driver
        </CardTitle>
        <CardDescription>
          Create your driver profile to start receiving job opportunities
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="bg-red-500/10 border border-red-500/20 rounded-lg p-3"
              >
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-400" />
                  <span className="text-red-400 text-sm">{error}</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Full Name */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Full Name *</label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Enter your full name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
                />
              </div>
            </div>

            {/* Father's Name */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Father's Name *</label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Enter your father's name"
                  value={formData.father_name}
                  onChange={(e) => handleInputChange('father_name', e.target.value)}
                  className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
                />
              </div>
            </div>
          </div>

          {/* Mobile Number */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Mobile Number *</label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="+92 300 1234567"
                value={formData.mobile_number}
                onChange={(e) => handleInputChange('mobile_number', e.target.value)}
                className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
              />
            </div>
          </div>

          {/* Education, Experience, and Marital Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Education Level *</label>
              <div className="relative">
                <GraduationCap className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground z-10" />
                <Select value={formData.education} onValueChange={(value: any) => handleInputChange('education', value)}>
                  <SelectTrigger className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20">
                    <SelectValue placeholder="Select education" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">Primary School</SelectItem>
                    <SelectItem value="secondary">Secondary School</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="bachelor">Bachelor's Degree</SelectItem>
                    <SelectItem value="master">Master's Degree</SelectItem>
                    <SelectItem value="phd">PhD</SelectItem>
                    <SelectItem value="diploma">Diploma</SelectItem>
                    <SelectItem value="certificate">Certificate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Experience (Years) *</label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="number"
                  placeholder="e.g., 5"
                  min="0"
                  max="50"
                  value={formData.experience_years}
                  onChange={(e) => handleInputChange('experience_years', e.target.value)}
                  className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Marital Status *</label>
              <div className="relative">
                <Heart className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground z-10" />
                <Select value={formData.marital_status} onValueChange={(value: any) => handleInputChange('marital_status', value)}>
                  <SelectTrigger className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single">Single</SelectItem>
                    <SelectItem value="married">Married</SelectItem>
                    <SelectItem value="divorced">Divorced</SelectItem>
                    <SelectItem value="widowed">Widowed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* City of Priority */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">City of Priority *</label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="e.g., Karachi, Lahore, Islamabad"
                value={formData.city_of_priority}
                onChange={(e) => handleInputChange('city_of_priority', e.target.value)}
                className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
              />
            </div>
          </div>

          {/* Bio */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Bio (Optional)</label>
            <Textarea
              placeholder="Tell us about yourself, your driving experience, and what makes you a great driver..."
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              className="focus:border-yellow-500 focus:ring-yellow-500/20 min-h-[100px]"
              maxLength={1000}
            />
            <p className="text-xs text-muted-foreground">{formData.bio.length}/1000 characters</p>
          </div>

          {/* Document Uploads */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <FileText className="h-5 w-5 text-yellow-500" />
              Required Documents
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Profile Picture */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Profile Picture (Optional)</label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleDocumentUpload(e, 'profile_picture_url')}
                    className="hidden"
                    id="profile_picture"
                  />
                  <label
                    htmlFor="profile_picture"
                    className={`flex items-center justify-center w-full h-12 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                      formData.profile_picture_url
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-yellow-500 hover:border-yellow-600 bg-yellow-500/5'
                    } ${uploadingDocument === 'profile_picture_url' ? 'opacity-50' : ''}`}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      <span className="text-sm">
                        {uploadingDocument === 'profile_picture_url'
                          ? 'Uploading...'
                          : formData.profile_picture_url
                            ? 'Profile Picture ✓'
                            : 'Upload Profile Picture'
                        }
                      </span>
                    </div>
                  </label>
                </div>
              </div>

              {/* CNIC Front */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">CNIC Front *</label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleDocumentUpload(e, 'cnic_front_url')}
                    className="hidden"
                    id="cnic_front"
                  />
                  <label
                    htmlFor="cnic_front"
                    className={`flex items-center justify-center w-full h-12 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                      formData.cnic_front_url
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-yellow-500 hover:border-yellow-600 bg-yellow-500/5'
                    } ${uploadingDocument === 'cnic_front_url' ? 'opacity-50' : ''}`}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      <span className="text-sm">
                        {uploadingDocument === 'cnic_front_url'
                          ? 'Uploading...'
                          : formData.cnic_front_url
                            ? 'CNIC Front ✓'
                            : 'Upload CNIC Front'
                        }
                      </span>
                    </div>
                  </label>
                </div>
              </div>

              {/* CNIC Back */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">CNIC Back *</label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleDocumentUpload(e, 'cnic_back_url')}
                    className="hidden"
                    id="cnic_back"
                  />
                  <label
                    htmlFor="cnic_back"
                    className={`flex items-center justify-center w-full h-12 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                      formData.cnic_back_url
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-yellow-500 hover:border-yellow-600 bg-yellow-500/5'
                    } ${uploadingDocument === 'cnic_back_url' ? 'opacity-50' : ''}`}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      <span className="text-sm">
                        {uploadingDocument === 'cnic_back_url'
                          ? 'Uploading...'
                          : formData.cnic_back_url
                            ? 'CNIC Back ✓'
                            : 'Upload CNIC Back'
                        }
                      </span>
                    </div>
                  </label>
                </div>
              </div>

              {/* Driving License */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Driving License *</label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleDocumentUpload(e, 'driving_license_url')}
                    className="hidden"
                    id="driving_license"
                  />
                  <label
                    htmlFor="driving_license"
                    className={`flex items-center justify-center w-full h-12 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                      formData.driving_license_url
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-yellow-500 hover:border-yellow-600 bg-yellow-500/5'
                    } ${uploadingDocument === 'driving_license_url' ? 'opacity-50' : ''}`}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      <span className="text-sm">
                        {uploadingDocument === 'driving_license_url'
                          ? 'Uploading...'
                          : formData.driving_license_url
                            ? 'Driving License ✓'
                            : 'Upload Driving License'
                        }
                      </span>
                    </div>
                  </label>
                </div>
              </div>

              {/* Electricity Bill */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Electricity Bill *</label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleDocumentUpload(e, 'electricity_bill_url')}
                    className="hidden"
                    id="electricity_bill"
                  />
                  <label
                    htmlFor="electricity_bill"
                    className={`flex items-center justify-center w-full h-12 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                      formData.electricity_bill_url
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-yellow-500 hover:border-yellow-600 bg-yellow-500/5'
                    } ${uploadingDocument === 'electricity_bill_url' ? 'opacity-50' : ''}`}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      <span className="text-sm">
                        {uploadingDocument === 'electricity_bill_url'
                          ? 'Uploading...'
                          : formData.electricity_bill_url
                            ? 'Electricity Bill ✓'
                            : 'Upload Electricity Bill'
                        }
                      </span>
                    </div>
                  </label>
                </div>
              </div>

              {/* Police Certificate */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">Police Certificate *</label>
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleDocumentUpload(e, 'police_certificate_url')}
                    className="hidden"
                    id="police_certificate"
                  />
                  <label
                    htmlFor="police_certificate"
                    className={`flex items-center justify-center w-full h-12 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                      formData.police_certificate_url
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-yellow-500 hover:border-yellow-600 bg-yellow-500/5'
                    } ${uploadingDocument === 'police_certificate_url' ? 'opacity-50' : ''}`}
                  >
                    <div className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      <span className="text-sm">
                        {uploadingDocument === 'police_certificate_url'
                          ? 'Uploading...'
                          : formData.police_certificate_url
                            ? 'Police Certificate ✓'
                            : 'Upload Police Certificate'
                        }
                      </span>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Info Note */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="w-5 h-5 rounded-full bg-yellow-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-black text-xs font-bold">i</span>
              </div>
              <div className="text-sm text-foreground">
                <p className="font-medium mb-1">Application Review Process</p>
                <p className="text-muted-foreground">
                  Your application and documents will be reviewed by our team within 3-5 business days.
                  We'll verify your identity, driving credentials, and background check.
                  You'll receive an email notification once your application is approved.
                </p>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black font-medium"
            >
              {isLoading ? 'Registering...' : 'Register as Driver'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
