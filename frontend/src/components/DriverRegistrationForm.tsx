/**
 * Driver Registration Form Component
 * Allows users to register as drivers with yellow theme
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Phone, MapPin, GraduationCap, Calendar, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface DriverFormData {
  name: string;
  mobile_number: string;
  education: 'primary' | 'secondary' | 'bachelors' | 'masters' | '';
  experience_years: string;
  city: string;
}

interface DriverRegistrationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const DriverRegistrationForm: React.FC<DriverRegistrationFormProps> = ({ onSuccess, onCancel }) => {
  const { user, isAuthenticated } = useAuth();
  const [formData, setFormData] = useState<DriverFormData>({
    name: user?.displayName || '',
    mobile_number: '',
    education: '',
    experience_years: '',
    city: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleInputChange = (field: keyof DriverFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) return 'Full name is required';
    if (!formData.mobile_number.trim()) return 'Mobile number is required';
    if (!formData.education) return 'Education level is required';
    if (!formData.experience_years || isNaN(Number(formData.experience_years))) return 'Valid experience years is required';
    if (!formData.city.trim()) return 'City is required';
    
    // Validate mobile number format (basic validation)
    const mobileRegex = /^(\+92|0)?[0-9]{10}$/;
    if (!mobileRegex.test(formData.mobile_number.replace(/\s/g, ''))) {
      return 'Please enter a valid mobile number';
    }

    const experience = Number(formData.experience_years);
    if (experience < 0 || experience > 50) {
      return 'Experience years must be between 0 and 50';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated) {
      setError('Please login to register as a driver');
      return;
    }

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const driverData = {
        ...formData,
        experience_years: Number(formData.experience_years),
      };

      const response = await apiService.createDriver(driverData);
      
      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          onSuccess?.();
        }, 2000);
      } else {
        setError(response.error || 'Failed to register as driver');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to register as driver');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center p-8 text-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring" }}
          className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mb-4"
        >
          <CheckCircle className="w-8 h-8 text-black" />
        </motion.div>
        <h3 className="text-xl font-bold text-foreground mb-2">Registration Successful!</h3>
        <p className="text-muted-foreground mb-4">Your driver profile has been created successfully.</p>
        <Button 
          onClick={onSuccess}
          className="bg-yellow-500 hover:bg-yellow-600 text-black"
        >
          View Driver Profiles
        </Button>
      </motion.div>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto border-0 bg-background/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-foreground">
          <User className="h-5 w-5 text-yellow-500" />
          Register as Driver
        </CardTitle>
        <CardDescription>
          Create your driver profile to start receiving job opportunities
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="bg-red-500/10 border border-red-500/20 rounded-lg p-3"
              >
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-400" />
                  <span className="text-red-400 text-sm">{error}</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Full Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Full Name *</label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Enter your full name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
              />
            </div>
          </div>

          {/* Mobile Number */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Mobile Number *</label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="+92 300 1234567"
                value={formData.mobile_number}
                onChange={(e) => handleInputChange('mobile_number', e.target.value)}
                className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
              />
            </div>
          </div>

          {/* Education and Experience */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Education Level *</label>
              <div className="relative">
                <GraduationCap className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground z-10" />
                <Select value={formData.education} onValueChange={(value: any) => handleInputChange('education', value)}>
                  <SelectTrigger className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20">
                    <SelectValue placeholder="Select education" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">Primary School</SelectItem>
                    <SelectItem value="secondary">Secondary School</SelectItem>
                    <SelectItem value="bachelors">Bachelor's Degree</SelectItem>
                    <SelectItem value="masters">Master's Degree</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Experience (Years) *</label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="number"
                  placeholder="e.g., 5"
                  min="0"
                  max="50"
                  value={formData.experience_years}
                  onChange={(e) => handleInputChange('experience_years', e.target.value)}
                  className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
                />
              </div>
            </div>
          </div>

          {/* City */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">City *</label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="e.g., Karachi, Lahore, Islamabad"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
              />
            </div>
          </div>

          {/* Info Note */}
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="w-5 h-5 rounded-full bg-yellow-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-black text-xs font-bold">i</span>
              </div>
              <div className="text-sm text-foreground">
                <p className="font-medium mb-1">Profile Verification</p>
                <p className="text-muted-foreground">
                  After registration, your profile will be reviewed for verification. 
                  You'll be notified once your profile is approved and visible to employers.
                </p>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black font-medium"
            >
              {isLoading ? 'Registering...' : 'Register as Driver'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
