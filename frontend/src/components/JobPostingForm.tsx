/**
 * Job Posting Form Component
 * Allows authenticated users to create job postings with yellow theme
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Briefcase, MapPin, DollarSign, Phone, Mail, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface JobFormData {
  title: string;
  description: string;
  category: 'household' | 'big_vehicles' | 'online_service' | 'company' | '';
  employment_type: 'part_time' | 'full_time' | '';
  package_amount: string;
  city: string;
  mobile_number: string;
  benefits: {
    residency: boolean;
    food: boolean;
    medical_insurance: boolean;
  };
  vehicle_provided: boolean;
}

interface JobPostingFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const JobPostingForm: React.FC<JobPostingFormProps> = ({ onSuccess, onCancel }) => {
  const { user, isAuthenticated } = useAuth();
  const [formData, setFormData] = useState<JobFormData>({
    title: '',
    description: '',
    category: '',
    employment_type: '',
    package_amount: '',
    city: '',
    mobile_number: '',
    benefits: {
      residency: false,
      food: false,
      medical_insurance: false,
    },
    vehicle_provided: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleInputChange = (field: keyof JobFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleBenefitChange = (benefit: keyof JobFormData['benefits'], checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      benefits: {
        ...prev.benefits,
        [benefit]: checked
      }
    }));
  };

  const validateForm = (): string | null => {
    if (!formData.title.trim()) return 'Job title is required';
    if (!formData.description.trim()) return 'Job description is required';
    if (!formData.category) return 'Job category is required';
    if (!formData.employment_type) return 'Employment type is required';
    if (!formData.package_amount || isNaN(Number(formData.package_amount))) return 'Valid package amount is required';
    if (!formData.city.trim()) return 'City is required';
    if (!formData.mobile_number.trim()) return 'Mobile number is required';
    
    // Validate mobile number format (basic validation)
    const mobileRegex = /^(\+92|0)?[0-9]{10}$/;
    if (!mobileRegex.test(formData.mobile_number.replace(/\s/g, ''))) {
      return 'Please enter a valid mobile number';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isAuthenticated) {
      setError('Please login to post a job');
      return;
    }

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const jobData = {
        ...formData,
        package_amount: Number(formData.package_amount),
        contact_email: user?.email || '', // Auto-filled from auth
      };

      const response = await apiService.createJob(jobData);
      
      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          onSuccess?.();
        }, 2000);
      } else {
        setError(response.error || 'Failed to create job posting');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to create job posting');
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center p-8 text-center"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring" }}
          className="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mb-4"
        >
          <CheckCircle className="w-8 h-8 text-black" />
        </motion.div>
        <h3 className="text-xl font-bold text-foreground mb-2">Job Posted Successfully!</h3>
        <p className="text-muted-foreground mb-4">Your job posting has been created and is now live.</p>
        <Button 
          onClick={onSuccess}
          className="bg-yellow-500 hover:bg-yellow-600 text-black"
        >
          View Job Listings
        </Button>
      </motion.div>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto border-0 bg-background/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-foreground">
          <Briefcase className="h-5 w-5 text-yellow-500" />
          Post a Job
        </CardTitle>
        <CardDescription>
          Create a job posting to find qualified drivers
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="bg-red-500/10 border border-red-500/20 rounded-lg p-3"
              >
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-400" />
                  <span className="text-red-400 text-sm">{error}</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Job Title */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Job Title *</label>
            <Input
              placeholder="e.g., Personal Driver, Delivery Driver"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="focus:border-yellow-500 focus:ring-yellow-500/20"
            />
          </div>

          {/* Job Description */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">Job Description *</label>
            <Textarea
              placeholder="Describe the job requirements, responsibilities, and any special requirements..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={4}
              className="focus:border-yellow-500 focus:ring-yellow-500/20"
            />
          </div>

          {/* Category and Employment Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Category *</label>
              <Select value={formData.category} onValueChange={(value: any) => handleInputChange('category', value)}>
                <SelectTrigger className="focus:border-yellow-500 focus:ring-yellow-500/20">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="household">Household Driver</SelectItem>
                  <SelectItem value="big_vehicles">Big Vehicles</SelectItem>
                  <SelectItem value="online_service">Online Service</SelectItem>
                  <SelectItem value="company">Company Driver</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Employment Type *</label>
              <Select value={formData.employment_type} onValueChange={(value: any) => handleInputChange('employment_type', value)}>
                <SelectTrigger className="focus:border-yellow-500 focus:ring-yellow-500/20">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="part_time">Part Time</SelectItem>
                  <SelectItem value="full_time">Full Time</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Package Amount and City */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Package Amount (PKR) *</label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="number"
                  placeholder="25000"
                  value={formData.package_amount}
                  onChange={(e) => handleInputChange('package_amount', e.target.value)}
                  className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">City *</label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="e.g., Karachi, Lahore"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Mobile Number *</label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="+92 300 1234567"
                  value={formData.mobile_number}
                  onChange={(e) => handleInputChange('mobile_number', e.target.value)}
                  className="pl-10 focus:border-yellow-500 focus:ring-yellow-500/20"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Email (Auto-filled)</label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  value={user?.email || ''}
                  disabled
                  className="pl-10 bg-muted/50"
                />
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div className="space-y-3">
            <label className="text-sm font-medium text-foreground">Benefits Offered</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="residency"
                  checked={formData.benefits.residency}
                  onCheckedChange={(checked) => handleBenefitChange('residency', !!checked)}
                  className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
                />
                <label htmlFor="residency" className="text-sm text-foreground">Residency</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="food"
                  checked={formData.benefits.food}
                  onCheckedChange={(checked) => handleBenefitChange('food', !!checked)}
                  className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
                />
                <label htmlFor="food" className="text-sm text-foreground">Food</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="medical"
                  checked={formData.benefits.medical_insurance}
                  onCheckedChange={(checked) => handleBenefitChange('medical_insurance', !!checked)}
                  className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
                />
                <label htmlFor="medical" className="text-sm text-foreground">Medical Insurance</label>
              </div>
            </div>
          </div>

          {/* Vehicle Provided */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="vehicle"
              checked={formData.vehicle_provided}
              onCheckedChange={(checked) => handleInputChange('vehicle_provided', !!checked)}
              className="data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500"
            />
            <label htmlFor="vehicle" className="text-sm text-foreground">Vehicle will be provided</label>
          </div>

          {/* Submit Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 bg-yellow-500 hover:bg-yellow-600 text-black font-medium"
            >
              {isLoading ? 'Posting...' : 'Post Job'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
