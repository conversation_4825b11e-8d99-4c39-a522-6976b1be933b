/**
 * Forum Message Form Component
 * Allows users to send messages in the community forum with yellow theme
 */

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, Mic, MapPin, AlertCircle, Volume2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';

interface ForumMessageFormProps {
  onMessageSent?: () => void;
  className?: string;
}

export const ForumMessageForm: React.FC<ForumMessageFormProps> = ({ onMessageSent, className }) => {
  const { isAuthenticated } = useAuth();
  const [message, setMessage] = useState('');
  const [city, setCity] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const cities = [
    'Karachi', 'Lahore', 'Islamabad', 'Rawalpindi', 'Faisalabad',
    'Multan', 'Peshawar', 'Quetta', 'Sialkot', 'Gujranwala'
  ];

  const handleSendMessage = async () => {
    if (!isAuthenticated) {
      setError('Please login to send messages');
      return;
    }

    if (!message.trim()) {
      setError('Please enter a message');
      return;
    }

    if (!city) {
      setError('Please select your city');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.sendForumMessage({
        content: message.trim(),
        message_type: 'text',
        city: city,
      });

      if (response.success) {
        setMessage('');
        onMessageSent?.();
      } else {
        setError(response.error || 'Failed to send message');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVoiceMessage = async () => {
    if (!isAuthenticated) {
      setError('Please login to send voice messages');
      return;
    }

    if (!city) {
      setError('Please select your city first');
      return;
    }

    setIsRecording(!isRecording);
    
    // Mock voice recording for demo
    if (!isRecording) {
      setTimeout(() => {
        setIsRecording(false);
        // Mock voice message
        handleSendVoiceMessage(15); // 15 seconds duration
      }, 3000);
    }
  };

  const handleSendVoiceMessage = async (duration: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiService.sendForumMessage({
        content: 'Voice message',
        message_type: 'voice',
        city: city,
        voice_duration: duration,
      });

      if (response.success) {
        onMessageSent?.();
      } else {
        setError(response.error || 'Failed to send voice message');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to send voice message');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Error Message */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/10 border border-red-500/20 rounded-lg p-3"
          >
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-400" />
              <span className="text-red-400 text-sm">{error}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* City Selection */}
      <div className="flex items-center gap-2">
        <MapPin className="h-4 w-4 text-yellow-500" />
        <Select value={city} onValueChange={setCity}>
          <SelectTrigger className="w-48 focus:border-yellow-500 focus:ring-yellow-500/20">
            <SelectValue placeholder="Select your city" />
          </SelectTrigger>
          <SelectContent>
            {cities.map((cityName) => (
              <SelectItem key={cityName} value={cityName}>
                {cityName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Message Input */}
      <div className="flex items-center gap-2">
        <div className="flex-1 relative">
          <Input
            placeholder={isAuthenticated ? "Share your thoughts with the community..." : "Please login to send messages"}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={!isAuthenticated || isLoading}
            className="pr-12 focus:border-yellow-500 focus:ring-yellow-500/20"
          />
          
          {/* Recording indicator */}
          {isRecording && (
            <motion.div
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1, repeat: Infinity }}
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
            >
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-xs text-red-500">Recording...</span>
              </div>
            </motion.div>
          )}
        </div>

        {/* Voice Message Button */}
        <Button
          type="button"
          size="icon"
          variant={isRecording ? "destructive" : "outline"}
          onClick={handleVoiceMessage}
          disabled={!isAuthenticated || isLoading}
          className={`${
            isRecording 
              ? 'bg-red-500 hover:bg-red-600' 
              : 'border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black'
          }`}
        >
          {isRecording ? (
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 0.5, repeat: Infinity }}
            >
              <Volume2 className="h-4 w-4" />
            </motion.div>
          ) : (
            <Mic className="h-4 w-4" />
          )}
        </Button>

        {/* Send Button */}
        <Button
          onClick={handleSendMessage}
          disabled={!isAuthenticated || isLoading || !message.trim() || !city}
          className="bg-yellow-500 hover:bg-yellow-600 text-black"
        >
          {isLoading ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              className="w-4 h-4 border-2 border-black border-t-transparent rounded-full"
            />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Help Text */}
      {!isAuthenticated && (
        <p className="text-xs text-muted-foreground">
          Please login to participate in community discussions
        </p>
      )}
      
      {isAuthenticated && !city && (
        <p className="text-xs text-yellow-600">
          Select your city to connect with local drivers
        </p>
      )}
    </div>
  );
};
