/**
 * Authentication Modal Component
 * Handles login, registration, and email verification
 */

import React, { useState, useEffect } from 'react';
import { authService } from '../../services/auth';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (user: any) => void;
}

type AuthMode = 'login' | 'register' | 'forgot-password' | 'verify-email';

export const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [mode, setMode] = useState<AuthMode>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [userType, setUserType] = useState<'driver' | 'employer'>('driver');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    // Initialize Google Sign-In when component mounts
    authService.initializeGoogleSignIn().catch(console.error);

    // Listen for Google Sign-In events
    const handleGoogleSuccess = (event: CustomEvent) => {
      onSuccess(event.detail.data);
      onClose();
    };

    const handleGoogleError = (event: CustomEvent) => {
      setError('Google sign-in failed: ' + event.detail.message);
    };

    window.addEventListener('googleSignInSuccess', handleGoogleSuccess as EventListener);
    window.addEventListener('googleSignInError', handleGoogleError as EventListener);

    return () => {
      window.removeEventListener('googleSignInSuccess', handleGoogleSuccess as EventListener);
      window.removeEventListener('googleSignInError', handleGoogleError as EventListener);
    };
  }, [onSuccess, onClose]);

  useEffect(() => {
    // Render Google Sign-In button when modal opens
    if (isOpen && (mode === 'login' || mode === 'register')) {
      setTimeout(() => {
        authService.renderGoogleSignInButton('google-signin-button');
      }, 100);
    }
  }, [isOpen, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      switch (mode) {
        case 'register':
          const registerResult = await authService.registerWithEmail(email, password, fullName, userType);
          setMessage('Registration successful! Please check your email for verification link.');
          setMode('verify-email');
          break;

        case 'forgot-password':
          await authService.sendPasswordReset(email);
          setMessage('Password reset email sent! Please check your email.');
          break;

        case 'verify-email':
          const verificationCode = authService.getVerificationCodeFromUrl();
          if (verificationCode) {
            await authService.verifyEmail(verificationCode);
            setMessage('Email verified successfully! You can now sign in.');
            setMode('login');
          } else {
            await authService.sendEmailVerification(email);
            setMessage('Verification email sent! Please check your email.');
          }
          break;

        default:
          setError('Please use Google Sign-In or register for a new account.');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      setLoading(true);
      await authService.sendEmailVerification(email);
      setMessage('Verification email sent! Please check your email.');
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {mode === 'login' && 'Sign In'}
              {mode === 'register' && 'Create Account'}
              {mode === 'forgot-password' && 'Reset Password'}
              {mode === 'verify-email' && 'Verify Email'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          {/* Messages */}
          {message && (
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              {message}
            </div>
          )}

          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {/* Google Sign-In Button */}
          {(mode === 'login' || mode === 'register') && (
            <div className="mb-6">
              <div id="google-signin-button" className="flex justify-center"></div>
              <div className="mt-4 text-center text-gray-500">
                <span className="bg-white px-2">or</span>
                <hr className="absolute left-6 right-6 top-1/2 -z-10" />
              </div>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your email"
              />
            </div>

            {/* Password (not for forgot-password or verify-email) */}
            {mode !== 'forgot-password' && mode !== 'verify-email' && (
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your password"
                  minLength={6}
                />
              </div>
            )}

            {/* Full Name (register only) */}
            {mode === 'register' && (
              <div>
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  id="fullName"
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your full name"
                />
              </div>
            )}

            {/* User Type (register only) */}
            {mode === 'register' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  I am a:
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="driver"
                      checked={userType === 'driver'}
                      onChange={(e) => setUserType(e.target.value as 'driver' | 'employer')}
                      className="mr-2"
                    />
                    Driver
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="employer"
                      checked={userType === 'employer'}
                      onChange={(e) => setUserType(e.target.value as 'driver' | 'employer')}
                      className="mr-2"
                    />
                    Employer
                  </label>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Please wait...' : (
                <>
                  {mode === 'login' && 'Sign In'}
                  {mode === 'register' && 'Create Account'}
                  {mode === 'forgot-password' && 'Send Reset Email'}
                  {mode === 'verify-email' && 'Send Verification Email'}
                </>
              )}
            </button>
          </form>

          {/* Mode Switching Links */}
          <div className="mt-6 text-center space-y-2">
            {mode === 'login' && (
              <>
                <p className="text-sm text-gray-600">
                  Don't have an account?{' '}
                  <button
                    onClick={() => setMode('register')}
                    className="text-blue-600 hover:underline"
                  >
                    Sign up
                  </button>
                </p>
                <p className="text-sm text-gray-600">
                  <button
                    onClick={() => setMode('forgot-password')}
                    className="text-blue-600 hover:underline"
                  >
                    Forgot password?
                  </button>
                </p>
              </>
            )}

            {mode === 'register' && (
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <button
                  onClick={() => setMode('login')}
                  className="text-blue-600 hover:underline"
                >
                  Sign in
                </button>
              </p>
            )}

            {mode === 'forgot-password' && (
              <p className="text-sm text-gray-600">
                Remember your password?{' '}
                <button
                  onClick={() => setMode('login')}
                  className="text-blue-600 hover:underline"
                >
                  Sign in
                </button>
              </p>
            )}

            {mode === 'verify-email' && (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Didn't receive the email?{' '}
                  <button
                    onClick={handleResendVerification}
                    disabled={loading}
                    className="text-blue-600 hover:underline disabled:opacity-50"
                  >
                    Resend
                  </button>
                </p>
                <p className="text-sm text-gray-600">
                  <button
                    onClick={() => setMode('login')}
                    className="text-blue-600 hover:underline"
                  >
                    Back to sign in
                  </button>
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
