/**
 * Authentication Context for Drive On
 * Manages user authentication state and provides auth methods
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { realFirebaseService as firebaseService } from '../services/firebase-real';
import type { User } from '../services/firebase';
import { apiService, AuthManager } from '../services/api';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signUp: (email: string, password: string, fullName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Listen for authentication state changes
    const unsubscribe = firebaseService.onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // Get Firebase ID token
          const idToken = await firebaseService.getIdToken();

          // Set token for API requests
          AuthManager.setToken(idToken);
          console.log('Authentication token set for user:', firebaseUser.email);

          // Authenticate with backend
          const response = await apiService.login(idToken);

          if (response.success) {
            setUser(firebaseUser);
          } else {
            console.error('Backend authentication failed:', response.error);
            setUser(null);
            AuthManager.clearToken();
          }
        } catch (error) {
          console.error('Authentication error:', error);
          setUser(null);
          AuthManager.clearToken();
        }
      } else {
        setUser(null);
        AuthManager.clearToken();
        console.log('User signed out, token cleared');
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const signIn = async (email: string, password: string): Promise<void> => {
    setLoading(true);
    try {
      const firebaseUser = await firebaseService.signInWithEmail(email, password);
      
      // Get Firebase ID token
      const idToken = await firebaseService.getIdToken();
      
      // Authenticate with backend
      const response = await apiService.login(idToken);
      
      if (response.success) {
        setUser(firebaseUser);
      } else {
        throw new Error(response.error || 'Backend authentication failed');
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = async (): Promise<void> => {
    setLoading(true);
    try {
      console.log('Starting Google sign-in...');

      // Sign in with Firebase
      const firebaseUser = await firebaseService.signInWithGoogle();
      console.log('Firebase sign-in successful:', firebaseUser);

      // Get Firebase ID token
      const idToken = await firebaseService.getIdToken();
      console.log('Got Firebase ID token');

      // Send to backend Firebase authentication endpoint
      const response = await fetch('http://localhost:8000/api/v1/auth-firebase/google-signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id_token: idToken
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('Backend authentication successful:', result);
        // Set the Firebase ID token for API authentication
        AuthManager.setToken(idToken);
        setUser(firebaseUser);
      } else {
        throw new Error(result.message || 'Backend authentication failed');
      }
    } catch (error: any) {
      console.error('Google sign in error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, fullName?: string): Promise<void> => {
    setLoading(true);
    try {
      console.log('Starting email registration...');

      // Register with backend Firebase endpoint
      const response = await fetch('http://localhost:8000/api/v1/auth-firebase/register-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          full_name: fullName,
          user_type: 'driver'
        })
      });

      const result = await response.json();

      if (result.success) {
        console.log('Registration successful:', result);
        // Note: User will need to verify email before signing in
        // Don't set user state yet - they need to verify email first
      } else {
        throw new Error(result.message || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    setLoading(true);
    try {
      await firebaseService.signOut();
      setUser(null);
      AuthManager.clearToken();
    } catch (error: any) {
      console.error('Sign out error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string): Promise<void> => {
    try {
      await firebaseService.resetPassword(email);
    } catch (error: any) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signInWithGoogle,
    signUp,
    signOut,
    resetPassword,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
