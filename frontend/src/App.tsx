import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Car, 
  Users, 
  Briefcase, 
  Newspaper, 
  MessageCircle, 
  Settings, 
  Star, 
  CheckCircle, 
  ArrowRight, 
  Menu, 
  X, 
  Phone, 
  Mail, 
  MapPin, 
  Send, 
  Mic, 
  Plus,
  Clock,
  DollarSign,
  Shield,
  Zap,
  Globe,
  Award,
  TrendingUp,
  ChevronDown,
  Play,
  Volume2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { YellowLoginDemo } from '@/components/YellowLoginDemo'
import { YellowSignUpCard } from '@/components/ui/sign-up-card-yellow'
import { AuthProvider, useAuth } from '@/contexts/AuthContext'
import { apiService } from '@/services/api'
import { JobPostingForm } from '@/components/JobPostingForm'
import { DriverRegistrationForm } from '@/components/DriverRegistrationForm'
import { ForumMessageForm } from '@/components/ForumMessageForm'

function AppContent() {
  const { user, signOut, isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState('home')
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [newMessage, setNewMessage] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [showLogin, setShowLogin] = useState(false)
  const [showJobForm, setShowJobForm] = useState(false)
  const [showDriverForm, setShowDriverForm] = useState(false)

  // Backend data states
  const [drivers, setDrivers] = useState<any[]>([])
  const [jobs, setJobs] = useState<any[]>([])
  const [newsItems, setNewsItems] = useState<any[]>([])
  const [forumMessages, setForumMessages] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  // Close login modal when user successfully authenticates
  useEffect(() => {
    if (isAuthenticated && showLogin) {
      setShowLogin(false);
    }
  }, [isAuthenticated, showLogin]);

  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [isDarkMode])

  // Fetch data from backend
  const fetchDrivers = async () => {
    try {
      setLoading(true);
      const response = await apiService.getDrivers({ limit: 10 });
      if (response.success) {
        setDrivers(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch drivers:', error);
      // Fallback to mock data
      setDrivers(mockDrivers);
    } finally {
      setLoading(false);
    }
  };

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await apiService.getJobs({ limit: 10 });
      if (response.success) {
        setJobs(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
      // Fallback to mock data
      setJobs(mockJobs);
    } finally {
      setLoading(false);
    }
  };

  const fetchNews = async () => {
    try {
      setLoading(true);
      const response = await apiService.getNews({ limit: 10 });
      if (response.success) {
        setNewsItems(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch news:', error);
      // Fallback to mock data
      setNewsItems(mockNewsItems);
    } finally {
      setLoading(false);
    }
  };

  const fetchForumMessages = async () => {
    // Only fetch forum messages if user is authenticated
    if (!isAuthenticated) {
      setForumMessages([]);
      return;
    }

    try {
      setLoading(true);
      const response = await apiService.getForumMessages();
      if (response.success) {
        setForumMessages(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch forum messages:', error);
      // Fallback to mock data for demo
      setForumMessages(mockMessages);
    } finally {
      setLoading(false);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDrivers();
    fetchJobs();
    fetchNews();
    fetchForumMessages();
  }, []);

  // Mock data as fallback
  const mockDrivers = [
    {
      id: '1',
      name: 'John Smith',
      rating: 4.9,
      experience: '5 years',
      location: 'New York',
      hourlyRate: 25,
      avatar: '/api/placeholder/40/40',
      verified: true
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      rating: 4.8,
      experience: '3 years',
      location: 'Los Angeles',
      hourlyRate: 22,
      avatar: '/api/placeholder/40/40',
      verified: true
    },
    {
      id: '3',
      name: 'Mike Wilson',
      rating: 4.7,
      experience: '7 years',
      location: 'Chicago',
      hourlyRate: 28,
      avatar: '/api/placeholder/40/40',
      verified: false
    }
  ]

  const mockJobs = [
    {
      id: '1',
      title: 'Personal Driver',
      company: 'Executive Services',
      location: 'Manhattan, NY',
      salary: '$50,000 - $65,000',
      type: 'Full-time',
      postedAt: '2 days ago',
      description: 'Looking for a professional driver for executive transportation.'
    },
    {
      id: '2',
      title: 'Delivery Driver',
      company: 'QuickDelivery Inc',
      location: 'Brooklyn, NY',
      salary: '$18 - $22/hour',
      type: 'Part-time',
      postedAt: '1 day ago',
      description: 'Flexible delivery driver position with competitive pay.'
    }
  ]

  const mockNewsItems = [
    {
      id: '1',
      title: 'Electric Vehicles: The Future of Transportation',
      image: '/api/placeholder/300/200',
      category: 'Technology',
      publishedAt: '2024-01-15',
      excerpt: 'Exploring the latest trends in electric vehicle technology and their impact on the driving industry.'
    },
    {
      id: '2',
      title: 'Gas Prices Drop to Lowest in 6 Months',
      image: '/api/placeholder/300/200',
      category: 'Fuel',
      publishedAt: '2024-01-14',
      excerpt: 'Analyzing the recent decrease in fuel prices and what it means for drivers nationwide.'
    }
  ]

  const mockMessages = [
    {
      id: '1',
      user: 'Alice Cooper',
      content: 'Anyone know good routes to avoid traffic in downtown?',
      timestamp: '10:30 AM',
      type: 'text',
      avatar: '/api/placeholder/32/32'
    },
    {
      id: '2',
      user: 'Bob Johnson',
      content: 'Voice message about traffic conditions',
      timestamp: '10:35 AM',
      type: 'voice',
      duration: 15,
      avatar: '/api/placeholder/32/32'
    }
  ]

  const features = [
    {
      icon: <Users className="h-8 w-8 text-yellow-500" />,
      title: 'Find Qualified Drivers',
      description: 'Connect with verified, experienced drivers in your area with our advanced matching system.'
    },
    {
      icon: <Briefcase className="h-8 w-8 text-yellow-500" />,
      title: 'Job Portal',
      description: 'Post driving jobs or find opportunities with our comprehensive job marketplace.'
    },
    {
      icon: <Newspaper className="h-8 w-8 text-yellow-500" />,
      title: 'Industry News',
      description: 'Stay updated with the latest automotive and fuel industry news and trends.'
    },
    {
      icon: <MessageCircle className="h-8 w-8 text-yellow-500" />,
      title: 'Community Forum',
      description: 'Connect with other drivers and share experiences through text and voice messages.'
    },
    {
      icon: <Shield className="h-8 w-8 text-yellow-500" />,
      title: 'Verified Profiles',
      description: 'All drivers undergo thorough background checks and verification processes.'
    },
    {
      icon: <Zap className="h-8 w-8 text-yellow-500" />,
      title: 'Instant Matching',
      description: 'Get matched with suitable drivers or jobs instantly using our AI-powered algorithm.'
    }
  ]

  const testimonials = [
    {
      name: 'Jennifer Martinez',
      role: 'Business Owner',
      content: 'Drive On helped me find the perfect driver for my company. The platform is intuitive and the drivers are professional.',
      avatar: '/api/placeholder/60/60',
      rating: 5
    },
    {
      name: 'David Chen',
      role: 'Professional Driver',
      content: 'I found multiple job opportunities through Drive On. The community forum is also great for networking.',
      avatar: '/api/placeholder/60/60',
      rating: 5
    },
    {
      name: 'Lisa Thompson',
      role: 'Fleet Manager',
      content: 'The verification process gives me confidence in hiring drivers. Excellent platform for our transportation needs.',
      avatar: '/api/placeholder/60/60',
      rating: 5
    }
  ]

  const pricingPlans = [
    {
      name: 'Basic',
      price: 'Free',
      period: 'forever',
      features: [
        'Browse drivers',
        'Basic job posting',
        'Community access',
        'News updates'
      ]
    },
    {
      name: 'Professional',
      price: '$29',
      period: 'per month',
      popular: true,
      features: [
        'Unlimited driver requests',
        'Priority job posting',
        'Advanced filters',
        'Direct messaging',
        'Background checks',
        'Premium support'
      ]
    },
    {
      name: 'Enterprise',
      price: '$99',
      period: 'per month',
      features: [
        'Everything in Professional',
        'Fleet management',
        'Custom integrations',
        'Dedicated account manager',
        'Analytics dashboard',
        'White-label solution'
      ]
    }
  ]

  const faqs = [
    {
      question: 'How do I verify my driver profile?',
      answer: 'To verify your profile, upload your driving license, insurance documents, and complete our background check process. Verification typically takes 24-48 hours.'
    },
    {
      question: 'What are the fees for posting jobs?',
      answer: 'Basic job posting is free for all users. Professional and Enterprise plans offer additional features like priority placement and advanced filtering.'
    },
    {
      question: 'How does the matching algorithm work?',
      answer: 'Our AI-powered algorithm considers location, experience, ratings, availability, and specific requirements to match drivers with suitable opportunities.'
    },
    {
      question: 'Is there a mobile app available?',
      answer: 'Yes, Drive On is available on both iOS and Android platforms with full feature parity to the web version.'
    }
  ]

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      setNewMessage('')
    }
  }

  const toggleRecording = () => {
    setIsRecording(!isRecording)
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'drivers':
        return (
          <div className="space-y-8">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold text-foreground mb-2">Find Drivers</h2>
                <p className="text-muted-foreground">Connect with verified professional drivers in your area</p>
              </div>
              <Button
                onClick={() => setShowDriverForm(true)}
                className="bg-yellow-500 hover:bg-yellow-600 text-black"
              >
                <Plus className="h-4 w-4 mr-2" />
                Register as Driver
              </Button>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search drivers by name or location..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Filter by..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Drivers</SelectItem>
                  <SelectItem value="verified">Verified Only</SelectItem>
                  <SelectItem value="experience">Most Experienced</SelectItem>
                  <SelectItem value="rating">Highest Rated</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {drivers.map((driver) => (
                <Card key={driver.id} className="hover:shadow-lg transition-all duration-300 border-0 bg-background/80 backdrop-blur-sm">
                  <CardHeader>
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={driver.avatar} alt={driver.name} />
                        <AvatarFallback>{driver.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <CardTitle className="text-lg">{driver.name}</CardTitle>
                          {driver.verified && (
                            <Badge variant="secondary" className="bg-yellow-500/10 text-yellow-600">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1 mt-1">
                          <Star className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                          <span className="text-sm font-medium">{driver.rating}</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Experience:</span>
                      <span className="font-medium">{driver.experience}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Location:</span>
                      <span className="font-medium">{driver.location}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Rate:</span>
                      <span className="font-medium">${driver.hourlyRate}/hour</span>
                    </div>
                    <Button className="w-full bg-yellow-500 hover:bg-yellow-600 text-black">
                      Contact Driver
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )

      case 'jobs':
        return (
          <div className="space-y-8">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold text-foreground mb-2">Job Portal</h2>
                <p className="text-muted-foreground">Find driving opportunities or post job openings</p>
              </div>
              <Button
                onClick={() => setShowJobForm(true)}
                className="bg-yellow-500 hover:bg-yellow-600 text-black"
              >
                <Plus className="h-4 w-4 mr-2" />
                Post a Job
              </Button>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Input
                placeholder="Search jobs by title or company..."
                className="flex-1"
              />
              <Select>
                <SelectTrigger className="w-full md:w-48">
                  <SelectValue placeholder="Job Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="full-time">Full-time</SelectItem>
                  <SelectItem value="part-time">Part-time</SelectItem>
                  <SelectItem value="contract">Contract</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-6">
              {jobs.map((job) => (
                <Card key={job.id} className="hover:shadow-lg transition-all duration-300 border-0 bg-background/80 backdrop-blur-sm">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div>
                        <CardTitle className="text-xl mb-2">{job.title}</CardTitle>
                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Briefcase className="h-4 w-4" />
                            {job.company}
                          </span>
                          <span className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {job.location}
                          </span>
                          <span className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            {job.salary}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {job.postedAt}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{job.type}</Badge>
                        <Button className="bg-yellow-500 hover:bg-yellow-600 text-black">
                          Apply Now
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{job.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )

      case 'news':
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-2">Industry News</h2>
              <p className="text-muted-foreground">Stay updated with the latest automotive and fuel industry trends</p>
            </div>

            <Tabs defaultValue="all" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="all">All News</TabsTrigger>
                <TabsTrigger value="technology">Technology</TabsTrigger>
                <TabsTrigger value="fuel">Fuel</TabsTrigger>
                <TabsTrigger value="regulations">Regulations</TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="mt-8">
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {newsItems.map((item) => (
                    <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 border-0 bg-background/80 backdrop-blur-sm">
                      <div className="aspect-video bg-muted relative overflow-hidden">
                        <img 
                          src={item.image} 
                          alt={item.title}
                          className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                        />
                        <Badge className="absolute top-4 left-4 bg-yellow-500 text-black">
                          {item.category}
                        </Badge>
                      </div>
                      <CardHeader>
                        <CardTitle className="text-lg line-clamp-2">{item.title}</CardTitle>
                        <div className="text-sm text-muted-foreground">
                          {new Date(item.publishedAt).toLocaleDateString()}
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground line-clamp-3">{item.excerpt}</p>
                        <Button variant="link" className="p-0 h-auto text-yellow-500 mt-2">
                          Read more <ArrowRight className="h-4 w-4 ml-1" />
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )

      case 'forum':
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-2">Community Forum</h2>
              <p className="text-muted-foreground">Connect with fellow drivers and share experiences</p>
            </div>

            <Card className="border-0 bg-background/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Recent Messages</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="max-h-96 overflow-y-auto space-y-4">
                  {forumMessages.map((message) => (
                    <div
                      key={message.id}
                      className="flex items-start space-x-3 p-3 rounded-lg bg-muted/50"
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={message.avatar} alt={message.user} />
                        <AvatarFallback>{message.user.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-sm">{message.user}</span>
                          <span className="text-xs text-muted-foreground">{message.timestamp}</span>
                        </div>
                        {message.type === 'text' ? (
                          <p className="text-sm">{message.content}</p>
                        ) : (
                          <div className="flex items-center gap-2 bg-yellow-500/10 p-2 rounded">
                            <Volume2 className="h-4 w-4 text-yellow-500" />
                            <span className="text-sm">Voice message ({message.duration}s)</span>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Play className="h-3 w-3" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <Separator />

                <ForumMessageForm
                  onMessageSent={fetchForumMessages}
                  className="mt-4"
                />
              </CardContent>
            </Card>
          </div>
        )

      case 'settings':
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-2">Settings</h2>
              <p className="text-muted-foreground">Manage your account preferences and application settings</p>
            </div>

            <div className="grid gap-6">
              <Card className="border-0 bg-background/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Appearance</CardTitle>
                  <CardDescription>Customize how Drive On looks and feels</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Dark Mode</div>
                      <div className="text-sm text-muted-foreground">Toggle between light and dark themes</div>
                    </div>
                    <Switch
                      checked={isDarkMode}
                      onCheckedChange={setIsDarkMode}
                      className="data-[state=checked]:bg-yellow-500"
                    />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 bg-background/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Notifications</CardTitle>
                  <CardDescription>Control what notifications you receive</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Job Alerts</div>
                      <div className="text-sm text-muted-foreground">Get notified about new job opportunities</div>
                    </div>
                    <Switch className="data-[state=checked]:bg-yellow-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Driver Requests</div>
                      <div className="text-sm text-muted-foreground">Receive notifications for driver requests</div>
                    </div>
                    <Switch className="data-[state=checked]:bg-yellow-500" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Forum Messages</div>
                      <div className="text-sm text-muted-foreground">Get notified about forum activity</div>
                    </div>
                    <Switch className="data-[state=checked]:bg-yellow-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 bg-background/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>Account</CardTitle>
                  <CardDescription>Manage your account information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {isAuthenticated ? (
                    <>
                      {/* User Profile Section */}
                      <div className="flex items-center space-x-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                        <div className="w-16 h-16 rounded-full bg-yellow-500 flex items-center justify-center overflow-hidden">
                          {user?.photoURL ? (
                            <img
                              src={user.photoURL}
                              alt="Profile"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <span className="text-black text-xl font-medium">
                              {user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                            </span>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {user?.displayName || 'User'}
                          </h3>
                          <p className="text-gray-600">{user?.email}</p>
                          <div className="flex items-center mt-1">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                            <span className="text-sm text-green-600">
                              {user?.emailVerified ? 'Email Verified' : 'Email Not Verified'}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Account Details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                          <Input
                            value={user?.displayName || ''}
                            readOnly
                            className="bg-gray-50"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                          <Input
                            value={user?.email || ''}
                            readOnly
                            className="bg-gray-50"
                          />
                        </div>
                      </div>

                      {/* User ID for debugging */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">User ID</label>
                        <Input
                          value={user?.uid || ''}
                          readOnly
                          className="bg-gray-50 font-mono text-xs"
                        />
                      </div>

                      {/* Authentication Status */}
                      <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                        <div className="flex items-center">
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                          <span className="text-green-800 font-medium">Successfully Authenticated</span>
                        </div>
                        <p className="text-green-600 text-sm mt-1">
                          You are signed in with Google and connected to Firebase
                        </p>
                      </div>
                    </>
                  ) : (
                    <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
                      <p className="text-gray-600">Please sign in to view account information</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )

      default:
        return (
          <>
            {/* Hero Section */}
            <motion.section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/20 dark:to-background">
              <div className="container mx-auto px-4 text-center relative z-10">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  className="max-w-4xl mx-auto"
                >
                  <h1 className="text-5xl md:text-7xl font-bold text-foreground mb-6">
                    Drive Your
                    <span className="text-yellow-500 block">Success Forward</span>
                  </h1>
                  
                  <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                    Connect with professional drivers, find driving opportunities, and stay updated with the latest automotive industry news.
                  </p>
                  
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button 
                      size="lg" 
                      className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold px-8 py-4 text-lg"
                      onClick={() => setActiveTab('drivers')}
                    >
                      Find Drivers
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                    <Button 
                      size="lg" 
                      variant="outline" 
                      className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black px-8 py-4 text-lg"
                      onClick={() => setActiveTab('jobs')}
                    >
                      Browse Jobs
                    </Button>
                  </div>
                </motion.div>
              </div>

              <motion.div
                className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <ChevronDown className="h-8 w-8 text-yellow-500" />
              </motion.div>
            </motion.section>

            {/* Proof Section */}
            <section className="py-20 bg-background">
              <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    Trusted by Thousands
                  </h2>
                  <p className="text-xl text-muted-foreground">
                    Join our growing community of drivers and employers
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                  {[
                    { number: '10K+', label: 'Active Drivers' },
                    { number: '5K+', label: 'Jobs Posted' },
                    { number: '98%', label: 'Success Rate' },
                    { number: '24/7', label: 'Support' }
                  ].map((stat, index) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl md:text-5xl font-bold text-yellow-500 mb-2">
                        {stat.number}
                      </div>
                      <div className="text-muted-foreground font-medium">
                        {stat.label}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </section>

            {/* Features Section */}
            <section className="py-20 bg-muted/30">
              <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    Everything You Need
                  </h2>
                  <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                    Comprehensive platform designed to connect drivers with opportunities and keep you informed about the industry.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {features.map((feature, index) => (
                    <Card key={index} className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-background/80 backdrop-blur-sm">
                      <CardHeader>
                        <div className="mb-4">
                          {feature.icon}
                        </div>
                        <CardTitle className="text-xl font-semibold">
                          {feature.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="text-base">
                          {feature.description}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </section>

            {/* Pricing Section */}
            <section className="py-20 bg-background">
              <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    Choose Your Plan
                  </h2>
                  <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                    Select the perfect plan for your driving needs and business requirements.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                  {pricingPlans.map((plan, index) => (
                    <Card key={index} className={`h-full border-0 shadow-lg ${plan.popular ? 'ring-2 ring-yellow-500' : ''} bg-background/80 backdrop-blur-sm relative`}>
                      {plan.popular && (
                        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-yellow-500 text-black px-4 py-1">
                            Most Popular
                          </Badge>
                        </div>
                      )}
                      <CardHeader className="text-center pb-8">
                        <CardTitle className="text-2xl font-bold">{plan.name}</CardTitle>
                        <div className="mt-4">
                          <span className="text-4xl font-bold text-foreground">{plan.price}</span>
                          <span className="text-muted-foreground ml-2">/{plan.period}</span>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <ul className="space-y-3">
                          {plan.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-center gap-3">
                              <CheckCircle className="h-5 w-5 text-yellow-500 flex-shrink-0" />
                              <span className="text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                        <Button 
                          className={`w-full mt-8 ${
                            plan.popular 
                              ? 'bg-yellow-500 hover:bg-yellow-600 text-black' 
                              : 'bg-background border border-border hover:bg-muted'
                          }`}
                        >
                          Get Started
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </section>

            {/* Testimonials Section */}
            <section className="py-20 bg-muted/30">
              <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    What Our Users Say
                  </h2>
                  <p className="text-xl text-muted-foreground">
                    Real feedback from our community of drivers and employers
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  {testimonials.map((testimonial, index) => (
                    <Card key={index} className="h-full border-0 shadow-lg bg-background/80 backdrop-blur-sm">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-1 mb-4">
                          {[...Array(testimonial.rating)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                          ))}
                        </div>
                        <p className="text-muted-foreground mb-6 italic">
                          "{testimonial.content}"
                        </p>
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                            <AvatarFallback>{testimonial.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-semibold">{testimonial.name}</div>
                            <div className="text-sm text-muted-foreground">{testimonial.role}</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </section>

            {/* FAQ Section */}
            <section className="py-20 bg-background">
              <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    Frequently Asked Questions
                  </h2>
                  <p className="text-xl text-muted-foreground">
                    Find answers to common questions about Drive On
                  </p>
                </div>

                <div className="max-w-3xl mx-auto">
                  <div className="space-y-4">
                    {faqs.map((faq, index) => (
                      <Card key={index} className="border-0 shadow-sm bg-background/80 backdrop-blur-sm">
                        <CardHeader>
                          <CardTitle className="text-lg">{faq.question}</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-muted-foreground">{faq.answer}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            </section>

            {/* Contact Section */}
            <section className="py-20 bg-muted/30">
              <div className="container mx-auto px-4">
                <div className="text-center mb-16">
                  <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                    Get in Touch
                  </h2>
                  <p className="text-xl text-muted-foreground">
                    Have questions? We'd love to hear from you.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-12 max-w-5xl mx-auto">
                  <Card className="border-0 shadow-lg bg-background/80 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle>Send us a message</CardTitle>
                      <CardDescription>
                        Fill out the form below and we'll get back to you as soon as possible.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <Input placeholder="First name" />
                        <Input placeholder="Last name" />
                      </div>
                      <Input placeholder="Email address" type="email" />
                      <Input placeholder="Subject" />
                      <Textarea placeholder="Your message" rows={4} />
                      <Button className="w-full bg-yellow-500 hover:bg-yellow-600 text-black">
                        Send Message
                        <Send className="ml-2 h-4 w-4" />
                      </Button>
                    </CardContent>
                  </Card>

                  <div className="space-y-8">
                    <div>
                      <h3 className="text-2xl font-bold text-foreground mb-6">Contact Information</h3>
                      <div className="space-y-4">
                        <div className="flex items-center gap-4">
                          <div className="bg-yellow-500/10 p-3 rounded-lg">
                            <Phone className="h-6 w-6 text-yellow-500" />
                          </div>
                          <div>
                            <div className="font-semibold">Phone</div>
                            <div className="text-muted-foreground">+****************</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="bg-yellow-500/10 p-3 rounded-lg">
                            <Mail className="h-6 w-6 text-yellow-500" />
                          </div>
                          <div>
                            <div className="font-semibold">Email</div>
                            <div className="text-muted-foreground"><EMAIL></div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <div className="bg-yellow-500/10 p-3 rounded-lg">
                            <MapPin className="h-6 w-6 text-yellow-500" />
                          </div>
                          <div>
                            <div className="font-semibold">Address</div>
                            <div className="text-muted-foreground">123 Drive Street, Auto City, AC 12345</div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold text-foreground mb-4">Business Hours</h4>
                      <div className="space-y-2 text-muted-foreground">
                        <div className="flex justify-between">
                          <span>Monday - Friday</span>
                          <span>9:00 AM - 6:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Saturday</span>
                          <span>10:00 AM - 4:00 PM</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Sunday</span>
                          <span>Closed</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Footer */}
            <footer className="bg-background border-t border-border">
              <div className="container mx-auto px-4 py-12">
                <div className="grid md:grid-cols-4 gap-8">
                  <div>
                    <div className="flex items-center space-x-2 mb-4">
                      <Car className="h-8 w-8 text-yellow-500" />
                      <span className="text-2xl font-bold text-foreground">Drive On</span>
                    </div>
                    <p className="text-muted-foreground mb-4">
                      Connecting drivers with opportunities and keeping the automotive community informed.
                    </p>
                    <div className="flex space-x-4">
                      {[Globe, Award, TrendingUp].map((Icon, index) => (
                        <div key={index} className="bg-yellow-500/10 p-2 rounded-lg">
                          <Icon className="h-5 w-5 text-yellow-500" />
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-foreground mb-4">Platform</h4>
                    <ul className="space-y-2 text-muted-foreground">
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Find Drivers</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Job Portal</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Industry News</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Community Forum</a></li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-foreground mb-4">Support</h4>
                    <ul className="space-y-2 text-muted-foreground">
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Help Center</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Contact Us</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Privacy Policy</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Terms of Service</a></li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-foreground mb-4">Company</h4>
                    <ul className="space-y-2 text-muted-foreground">
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">About Us</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Careers</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Press</a></li>
                      <li><a href="#" className="hover:text-yellow-500 transition-colors">Partners</a></li>
                    </ul>
                  </div>
                </div>

                <Separator className="my-8" />

                <div className="flex flex-col md:flex-row justify-between items-center">
                  <p className="text-muted-foreground text-sm">
                    © 2024 Drive On. All rights reserved.
                  </p>
                  <p className="text-muted-foreground text-sm">
                    Made with ❤️ for the driving community
                  </p>
                </div>
              </div>
            </footer>
          </>
        )
    }
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'dark' : ''}`}>
      <div className="bg-background text-foreground">
        {/* Navigation */}
        <motion.nav 
          className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-lg border-b border-border"
          initial={{ y: -100 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between h-16">
              <motion.div 
                className="flex items-center space-x-2"
                whileHover={{ scale: 1.05 }}
              >
                <Car className="h-8 w-8 text-yellow-500" />
                <span className="text-2xl font-bold text-foreground">Drive On</span>
              </motion.div>

              <div className="hidden md:flex items-center space-x-8">
                {['home', 'drivers', 'jobs', 'news', 'forum', 'settings'].map((tab) => (
                  <motion.button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`capitalize font-medium transition-colors ${
                      activeTab === tab ? 'text-yellow-500' : 'text-muted-foreground hover:text-foreground'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {tab}
                  </motion.button>
                ))}
              </div>

              <div className="flex items-center space-x-4">
                {isAuthenticated ? (
                  <div className="hidden md:flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center">
                        <span className="text-black text-sm font-medium">
                          {user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                        </span>
                      </div>
                      <span className="text-sm text-foreground">
                        {user?.displayName || user?.email?.split('@')[0]}
                      </span>
                    </div>
                    <Button
                      onClick={signOut}
                      variant="outline"
                      size="sm"
                      className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black"
                    >
                      Logout
                    </Button>
                  </div>
                ) : (
                  <Button
                    onClick={() => setShowLogin(true)}
                    className="bg-yellow-500 hover:bg-yellow-600 text-black font-medium px-4 py-2 hidden md:flex"
                  >
                    Login
                  </Button>
                )}
                <Switch
                  checked={isDarkMode}
                  onCheckedChange={setIsDarkMode}
                  className="data-[state=checked]:bg-yellow-500"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                >
                  {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
                </Button>
              </div>
            </div>
          </div>

          <AnimatePresence>
            {isMenuOpen && (
              <motion.div
                className="md:hidden bg-background border-t border-border"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="container mx-auto px-4 py-4 space-y-2">
                  {['home', 'drivers', 'jobs', 'news', 'forum', 'settings'].map((tab) => (
                    <button
                      key={tab}
                      onClick={() => {
                        setActiveTab(tab)
                        setIsMenuOpen(false)
                      }}
                      className={`block w-full text-left px-4 py-2 rounded-lg capitalize font-medium transition-colors ${
                        activeTab === tab ? 'bg-yellow-500/10 text-yellow-500' : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                      }`}
                    >
                      {tab}
                    </button>
                  ))}
                  {isAuthenticated ? (
                    <div className="mt-4 p-4 border-t border-border">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-10 h-10 rounded-full bg-yellow-500 flex items-center justify-center">
                          <span className="text-black text-sm font-medium">
                            {user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-foreground">
                            {user?.displayName || 'User'}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {user?.email}
                          </div>
                        </div>
                      </div>
                      <Button
                        onClick={() => {
                          signOut()
                          setIsMenuOpen(false)
                        }}
                        variant="outline"
                        className="w-full border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black"
                      >
                        Logout
                      </Button>
                    </div>
                  ) : (
                    <Button
                      onClick={() => {
                        setShowLogin(true)
                        setIsMenuOpen(false)
                      }}
                      className="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-medium mt-4"
                    >
                      Login
                    </Button>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.nav>

        {/* Main Content */}
        {activeTab === 'home' ? (
          renderContent()
        ) : (
          <div className="pt-24 pb-12">
            <div className="container mx-auto px-4">
              {renderContent()}
            </div>
          </div>
        )}
      </div>

      {/* Login Modal */}
      <AnimatePresence>
        {showLogin && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50"
            onClick={() => setShowLogin(false)}
          >
            <div onClick={(e) => e.stopPropagation()}>
              <YellowLoginDemo />
            </div>
            {/* Close button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 text-white hover:bg-white/10"
              onClick={() => setShowLogin(false)}
            >
              <X className="h-6 w-6" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Job Posting Modal */}
      <AnimatePresence>
        {showJobForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setShowJobForm(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-4xl max-h-[90vh] overflow-y-auto"
            >
              <JobPostingForm
                onSuccess={() => {
                  setShowJobForm(false);
                  fetchJobs();
                  setActiveTab('jobs');
                }}
                onCancel={() => setShowJobForm(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Driver Registration Modal */}
      <AnimatePresence>
        {showDriverForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setShowDriverForm(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-4xl max-h-[90vh] overflow-y-auto"
            >
              <DriverRegistrationForm
                onSuccess={() => {
                  setShowDriverForm(false);
                  fetchDrivers();
                  setActiveTab('drivers');
                }}
                onCancel={() => setShowDriverForm(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Main App component with AuthProvider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App
