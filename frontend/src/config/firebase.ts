/**
 * Firebase Configuration for Drive On
 * Real Firebase SDK integration
 */

import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { logFirebaseStatus } from '../utils/firebase-checker';

// Firebase configuration for Drive On (Real Project)
// Using your actual Firebase project configuration
const firebaseConfig = {
  apiKey: "AIzaSyDZgwF_CQI1ZJX1C1Jqvf-R0fbhq-8ZgcM",
  authDomain: "drive-on-b2af8.firebaseapp.com",
  databaseURL: "https://drive-on-b2af8-default-rtdb.firebaseio.com",
  projectId: "drive-on-b2af8",
  storageBucket: "drive-on-b2af8.firebasestorage.app",
  messagingSenderId: "206767723448",
  appId: "1:206767723448:web:01e966406d003fcc2d754d",
  measurementId: "G-502XMVPTC8"
};

// Log Firebase configuration status
logFirebaseStatus();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Initialize Firebase Storage and get a reference to the service
export const storage = getStorage(app);

// Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

export default app;
