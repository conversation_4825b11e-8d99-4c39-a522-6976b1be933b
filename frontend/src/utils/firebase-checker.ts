/**
 * Firebase Configuration Checker
 * Validates Firebase setup and provides helpful error messages
 */

export interface FirebaseConfigStatus {
  isValid: boolean;
  missingKeys: string[];
  warnings: string[];
  canUseAuth: boolean;
  canUseStorage: boolean;
  canUseFirestore: boolean;
}

export function checkFirebaseConfig(): FirebaseConfigStatus {
  const config = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
    appId: import.meta.env.VITE_FIREBASE_APP_ID
  };

  const missingKeys: string[] = [];
  const warnings: string[] = [];

  // Check for missing or placeholder values
  if (!config.apiKey || config.apiKey.includes('Your-Web-API-Key')) {
    missingKeys.push('VITE_FIREBASE_API_KEY');
  }

  if (!config.appId || config.appId.includes('your-web-app-id')) {
    missingKeys.push('VITE_FIREBASE_APP_ID');
  }

  if (!config.authDomain) {
    missingKeys.push('VITE_FIREBASE_AUTH_DOMAIN');
  }

  if (!config.projectId) {
    missingKeys.push('VITE_FIREBASE_PROJECT_ID');
  }

  if (!config.storageBucket) {
    missingKeys.push('VITE_FIREBASE_STORAGE_BUCKET');
  }

  if (!config.messagingSenderId) {
    missingKeys.push('VITE_FIREBASE_MESSAGING_SENDER_ID');
  }

  // Determine what features can be used
  const canUseAuth = !missingKeys.includes('VITE_FIREBASE_API_KEY') && !missingKeys.includes('VITE_FIREBASE_AUTH_DOMAIN');
  const canUseStorage = !missingKeys.includes('VITE_FIREBASE_STORAGE_BUCKET') && canUseAuth;
  const canUseFirestore = !missingKeys.includes('VITE_FIREBASE_PROJECT_ID') && canUseAuth;

  // Add warnings
  if (missingKeys.length > 0) {
    warnings.push('Some Firebase configuration values are missing. Check your .env.local file.');
  }

  if (!canUseAuth) {
    warnings.push('Authentication features will not work without proper Firebase configuration.');
  }

  return {
    isValid: missingKeys.length === 0,
    missingKeys,
    warnings,
    canUseAuth,
    canUseStorage,
    canUseFirestore
  };
}

export function logFirebaseStatus(): void {
  const status = checkFirebaseConfig();
  
  console.log('🔥 Firebase Configuration Status:');
  
  if (status.isValid) {
    console.log('✅ Firebase is properly configured');
  } else {
    console.warn('⚠️ Firebase configuration issues detected:');
    status.missingKeys.forEach(key => {
      console.warn(`   Missing: ${key}`);
    });
  }

  if (status.warnings.length > 0) {
    console.warn('⚠️ Warnings:');
    status.warnings.forEach(warning => {
      console.warn(`   ${warning}`);
    });
  }

  console.log('📋 Available features:');
  console.log(`   Authentication: ${status.canUseAuth ? '✅' : '❌'}`);
  console.log(`   Storage: ${status.canUseStorage ? '✅' : '❌'}`);
  console.log(`   Firestore: ${status.canUseFirestore ? '✅' : '❌'}`);

  if (!status.isValid) {
    console.log('');
    console.log('🔧 To fix this:');
    console.log('1. Open Firebase Console: https://console.firebase.google.com/project/drive-on-b2af8');
    console.log('2. Go to Project Settings > General > Your apps');
    console.log('3. Create a web app if none exists');
    console.log('4. Copy the config values to your .env.local file');
    console.log('5. Restart your development server');
  }
}

export function getFirebaseSetupInstructions(): string {
  return `
🔥 Firebase Setup Instructions for Drive On

1. Open Firebase Console:
   https://console.firebase.google.com/project/drive-on-b2af8

2. Go to Project Settings (⚙️) > General tab

3. Scroll to "Your apps" section

4. If no web app exists:
   - Click "Add app" button
   - Select Web (</> icon)
   - Enter nickname: "Drive On Web"
   - Click "Register app"

5. Copy the configuration values:
   - apiKey
   - appId
   - authDomain
   - projectId
   - storageBucket
   - messagingSenderId

6. Update your .env.local file with these values

7. Enable Authentication:
   - Go to Authentication > Sign-in method
   - Enable Google provider
   - Add localhost to authorized domains

8. Enable Firestore:
   - Go to Firestore Database
   - Create database in test mode

9. Enable Storage:
   - Go to Storage
   - Get started in test mode

10. Restart your development server: npm run dev
  `;
}
