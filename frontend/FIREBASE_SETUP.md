# Firebase Setup Guide for Drive On

## 🔥 Getting Your Firebase Configuration

To enable Google Sign-In and real Firebase authentication, you need to get your Firebase web app configuration.

### Step 1: Go to Firebase Console
1. Open [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **drive-on-b2af8**

### Step 2: Get Web App Configuration
1. Click on the **Settings gear icon** (⚙️) in the left sidebar
2. Select **Project settings**
3. Scroll down to **Your apps** section
4. If you don't have a web app yet:
   - Click **Add app** button
   - Select **Web** (</> icon)
   - Enter app nickname: "Drive On Web"
   - Check **"Also set up Firebase Hosting"** (optional)
   - Click **Register app**

5. Copy the `firebaseConfig` object that looks like this:
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyD...",
  authDomain: "drive-on-b2af8.firebaseapp.com",
  projectId: "drive-on-b2af8",
  storageBucket: "drive-on-b2af8.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

### Step 3: Update Firebase Config
1. Open `frontend/src/config/firebase.ts`
2. Replace the `firebaseConfig` object with your actual configuration

### Step 4: Enable Google Sign-In
1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Click on **Google** provider
3. Click **Enable**
4. Add your email as an authorized domain if needed
5. Copy the **Web client ID** (you'll need this for Google Sign-In)

### Step 5: Add Authorized Domains
1. In **Authentication** > **Settings** > **Authorized domains**
2. Add these domains:
   - `localhost` (for development)
   - Your production domain (when you deploy)

### Step 6: Update Google Client ID
1. Open `frontend/src/services/auth.ts`
2. Find the line with `client_id: 'YOUR_GOOGLE_CLIENT_ID'`
3. Replace with your actual Google Client ID from Step 4

## 🚀 Quick Test Setup (Development Only)

For immediate testing, I'll create a development configuration that uses your project ID but with mock authentication.

## 📱 Testing Google Sign-In

Once configured:
1. Start your frontend: `npm run dev`
2. Click the "Sign in with Google" button
3. You should see the Google account selection popup
4. After signing in, the user will be authenticated with your backend

## 🔧 Troubleshooting

### Popup Blocked
- Make sure your browser allows popups for localhost
- Try disabling popup blockers

### CORS Errors
- Make sure localhost is in your authorized domains
- Check that your Firebase config is correct

### Authentication Errors
- Verify your Google Client ID is correct
- Check that Google sign-in is enabled in Firebase Console
- Make sure your backend Firebase service account has the right permissions

## 🔐 Security Notes

- Never commit your actual Firebase config to public repositories
- Use environment variables for production
- Keep your service account key secure
