"""
Drive On Backend API
FastAPI application for the Drive On platform
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import uvicorn

from app.core.config import settings
from app.core.firebase import initialize_firebase
from app.api.v1 import auth, drivers, jobs, news, forum, uploads, admin, messaging, notifications, applications, firestore_api, auth_firebase


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting Drive On API...")
    initialize_firebase()
    print("✅ Firebase initialized")
    
    # Start news scraping scheduler
    try:
        from app.services.scheduler import start_news_scheduler
        start_news_scheduler()
        print("✅ News scraping scheduler started")
    except Exception as e:
        print(f"⚠️ Failed to start news scheduler: {str(e)}")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Drive On API...")
    try:
        from app.services.scheduler import stop_news_scheduler
        stop_news_scheduler()
        print("✅ News scraping scheduler stopped")
    except Exception as e:
        print(f"⚠️ Failed to stop news scheduler: {str(e)}")


# Create FastAPI application
app = FastAPI(
    title="Drive On API",
    description="Backend API for the Drive On platform - connecting drivers with opportunities",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Global exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}"
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error_code": "INTERNAL_ERROR"
        }
    )


# Health check endpoint
@app.get("/")
async def root():
    return {
        "success": True,
        "message": "Drive On API is running",
        "data": {
            "version": "1.0.0",
            "status": "healthy"
        }
    }


@app.get("/health")
async def health_check():
    return {
        "success": True,
        "message": "API is healthy",
        "data": {
            "status": "ok",
            "version": "1.0.0"
        }
    }


# Include API routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(drivers.router, prefix="/api/v1/drivers", tags=["Drivers"])
app.include_router(jobs.router, prefix="/api/v1/jobs", tags=["Jobs"])
app.include_router(applications.router, prefix="/api/v1/applications", tags=["Job Applications"])
app.include_router(news.router, prefix="/api/v1/news", tags=["News"])
app.include_router(forum.router, prefix="/api/v1/forum", tags=["Forum"])
app.include_router(uploads.router, prefix="/api/v1/uploads", tags=["File Uploads"])
app.include_router(messaging.router, prefix="/api/v1/messaging", tags=["Private Messaging"])
app.include_router(notifications.router, prefix="/api/v1/notifications", tags=["Notifications"])
app.include_router(admin.router, prefix="/api/v1/admin", tags=["Admin"])

# Firebase Authentication endpoints
app.include_router(auth_firebase.router, prefix="/api/v1/auth-firebase", tags=["Firebase Authentication"])

# Firestore-based API endpoints (simplified)
app.include_router(firestore_api.router, prefix="/api/v1/firestore", tags=["Firestore API"])


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
