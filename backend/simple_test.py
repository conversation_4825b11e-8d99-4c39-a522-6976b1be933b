#!/usr/bin/env python3
"""
Simple Backend Testing Script for Drive On API
Tests basic API endpoints without Firebase dependencies
"""

import requests
import json
import time
from typing import Dict, Any

class SimpleAPITester:
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   Details: {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    def make_request(self, method: str, endpoint: str, data: Any = None, 
                    headers: Dict = None, auth_required: bool = False) -> Dict:
        """Make HTTP request"""
        url = f"{self.base_url}{endpoint}"
        
        # Default headers
        request_headers = {"Content-Type": "application/json"}
        if headers:
            request_headers.update(headers)
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=request_headers, params=data, timeout=10)
            elif method.upper() == "POST":
                response = requests.post(url, headers=request_headers, json=data, timeout=10)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=request_headers, json=data, timeout=10)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=request_headers, timeout=10)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            return {
                "status_code": response.status_code,
                "data": response.json() if response.content else {},
                "success": 200 <= response.status_code < 300
            }
        except requests.exceptions.ConnectionError:
            return {
                "status_code": 0,
                "data": {"error": "Connection refused - server not running"},
                "success": False
            }
        except Exception as e:
            return {
                "status_code": 0,
                "data": {"error": str(e)},
                "success": False
            }
    
    def test_server_connection(self):
        """Test if server is running"""
        print("\n🔌 Testing Server Connection...")
        response = self.make_request("GET", "/")
        if response["status_code"] == 0:
            self.log_test("Server Connection", False, "Server is not running")
            return False
        else:
            self.log_test("Server Connection", True, f"Server responding on {self.base_url}")
            return True
    
    def test_health_endpoints(self):
        """Test health check endpoints"""
        print("\n🏥 Testing Health Endpoints...")
        
        # Test root endpoint
        response = self.make_request("GET", "/")
        success = response["success"] and response["data"].get("success") == True
        self.log_test("Root Endpoint", success, f"Status: {response['status_code']}")
        
        # Test health endpoint
        response = self.make_request("GET", "/health")
        success = response["success"] and response["data"].get("success") == True
        self.log_test("Health Check", success, f"Status: {response['status_code']}")
    
    def test_api_structure(self):
        """Test API documentation endpoints"""
        print("\n📚 Testing API Documentation...")
        
        # Test OpenAPI docs
        response = self.make_request("GET", "/docs")
        success = response["status_code"] in [200, 404]  # 404 is ok if docs not enabled
        self.log_test("OpenAPI Docs", success, f"Status: {response['status_code']}")
        
        # Test OpenAPI JSON
        response = self.make_request("GET", "/openapi.json")
        success = response["status_code"] in [200, 404]
        self.log_test("OpenAPI JSON", success, f"Status: {response['status_code']}")
    
    def test_basic_endpoints(self):
        """Test basic API endpoints without authentication"""
        print("\n🔍 Testing Basic API Endpoints...")
        
        endpoints_to_test = [
            ("/api/v1/drivers/", "GET", "Get Drivers"),
            ("/api/v1/jobs/", "GET", "Get Jobs"),
            ("/api/v1/news/", "GET", "Get News"),
            ("/api/v1/forum/", "GET", "Get Forums"),
            ("/api/v1/news/categories", "GET", "Get News Categories"),
        ]
        
        for endpoint, method, name in endpoints_to_test:
            response = self.make_request(method, endpoint)
            # Accept various status codes as success (200, 401 for auth required, etc.)
            success = response["status_code"] in [200, 401, 422, 404]
            self.log_test(name, success, f"Status: {response['status_code']}")
    
    def test_auth_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")
        
        # Test token verification (should fail with invalid token)
        response = self.make_request("POST", "/api/v1/auth/verify-token", 
                                   {"token": "invalid_token"})
        success = response["status_code"] in [400, 401, 422]  # Expected to fail
        self.log_test("Token Verification", success, f"Status: {response['status_code']}")
        
        # Test get current user (should fail without auth)
        response = self.make_request("GET", "/api/v1/auth/me")
        success = response["status_code"] in [401, 403]  # Expected to fail
        self.log_test("Get Current User (No Auth)", success, f"Status: {response['status_code']}")
    
    def test_post_endpoints(self):
        """Test POST endpoints with sample data"""
        print("\n📝 Testing POST Endpoints...")
        
        # Test driver registration (should fail without auth or with validation errors)
        driver_data = {
            "name": "Test Driver",
            "mobile_number": "+923001234567",
            "education": "bachelors",
            "experience_years": 5
        }
        response = self.make_request("POST", "/api/v1/drivers/register", driver_data)
        success = response["status_code"] in [200, 201, 400, 401, 422]
        self.log_test("Driver Registration", success, f"Status: {response['status_code']}")
        
        # Test job creation (should fail without auth)
        job_data = {
            "title": "Test Job",
            "description": "Test job description",
            "location": "Test City"
        }
        response = self.make_request("POST", "/api/v1/jobs/", job_data)
        success = response["status_code"] in [200, 201, 400, 401, 422]
        self.log_test("Job Creation", success, f"Status: {response['status_code']}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Simple Backend Testing...")
        print(f"Testing API at: {self.base_url}")
        print("=" * 60)
        
        # Check if server is running first
        if not self.test_server_connection():
            print("\n❌ Server is not running. Please start the backend server first.")
            return
        
        # Run all test categories
        self.test_health_endpoints()
        self.test_api_structure()
        self.test_basic_endpoints()
        self.test_auth_endpoints()
        self.test_post_endpoints()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['details']}")
        
        print("\n📝 Notes:")
        print("   - This is a basic connectivity and structure test")
        print("   - Many endpoints expect authentication and will return 401")
        print("   - 401/403 responses for protected endpoints are expected")
        print("   - Connection errors indicate the server is not running")


def main():
    """Main function to run tests"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Drive On Backend API")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API (default: http://localhost:8000)")
    
    args = parser.parse_args()
    
    # Create tester instance
    tester = SimpleAPITester(args.url)
    
    # Run all tests
    tester.run_all_tests()


if __name__ == "__main__":
    main()
