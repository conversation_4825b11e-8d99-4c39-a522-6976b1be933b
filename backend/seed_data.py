#!/usr/bin/env python3
"""
Data Seeder for Drive On
Adds sample data to the database for testing
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.core.database import engine, get_db
from app.models.user import User, Driver
from app.models.job import Job
from app.models.news import NewsArticle, NewsSource, NewsCategory
from app.models.forum import Forum, ForumMessage

def seed_data():
    """Add sample data to the database"""
    print("🌱 Seeding database with sample data...")
    
    db = Session(bind=engine)
    
    try:
        # Create sample users
        print("👥 Creating sample users...")
        
        # Sample employer user
        employer_user = User(
            firebase_uid="employer_123",
            email="<EMAIL>",
            full_name="<PERSON> Employer",
            phone="+************",
            user_type="employer",
            is_active=True,
            is_verified=True,
            created_at=datetime.now()
        )
        db.add(employer_user)
        db.flush()  # Get the ID
        
        # Sample driver user
        driver_user = User(
            firebase_uid="driver_123",
            email="<EMAIL>",
            full_name="<PERSON> Khan",
            phone="+************",
            user_type="driver",
            is_active=True,
            is_verified=True,
            created_at=datetime.now()
        )
        db.add(driver_user)
        db.flush()  # Get the ID
        
        # Create sample driver profile
        print("🚗 Creating sample driver profile...")
        driver_profile = Driver(
            user_id=driver_user.id,
            name="Ahmed Khan",
            father_name="Muhammad Khan",
            mobile_number="+************",
            education="bachelors",
            experience_years=5,
            marital_status="married",
            city_of_priority="Karachi",
            city="Karachi",
            country="Pakistan",
            is_available=True,
            application_status="approved",
            background_check_status="cleared",
            license_verified=True,
            documents_verified=True,
            insurance_verified=True,
            cnic_front_url="https://example.com/cnic_front.jpg",
            cnic_back_url="https://example.com/cnic_back.jpg",
            driving_license_url="https://example.com/license.jpg",
            electricity_bill_url="https://example.com/bill.jpg",
            police_certificate_url="https://example.com/police.jpg",
            bio="Experienced and professional driver with 5 years of experience.",
            total_jobs_completed=15,
            average_rating=4.5,
            total_ratings=12,
            created_at=datetime.now(),
            approved_at=datetime.now()
        )
        db.add(driver_profile)
        
        # Create another driver
        driver_user2 = User(
            firebase_uid="driver_456",
            email="<EMAIL>",
            full_name="Ali Hassan",
            phone="+************",
            user_type="driver",
            is_active=True,
            is_verified=True,
            created_at=datetime.now()
        )
        db.add(driver_user2)
        db.flush()
        
        driver_profile2 = Driver(
            user_id=driver_user2.id,
            name="Ali Hassan",
            father_name="Hassan Ali",
            mobile_number="+************",
            education="secondary",
            experience_years=3,
            marital_status="single",
            city_of_priority="Lahore",
            city="Lahore",
            country="Pakistan",
            is_available=True,
            application_status="approved",
            background_check_status="cleared",
            license_verified=True,
            documents_verified=True,
            insurance_verified=True,
            cnic_front_url="https://example.com/cnic_front2.jpg",
            cnic_back_url="https://example.com/cnic_back2.jpg",
            driving_license_url="https://example.com/license2.jpg",
            electricity_bill_url="https://example.com/bill2.jpg",
            police_certificate_url="https://example.com/police2.jpg",
            bio="Reliable driver with clean driving record.",
            total_jobs_completed=8,
            average_rating=4.2,
            total_ratings=6,
            created_at=datetime.now(),
            approved_at=datetime.now()
        )
        db.add(driver_profile2)
        
        # Create sample jobs
        print("💼 Creating sample jobs...")
        job1 = Job(
            employer_id=employer_user.id,
            title="Personal Driver Needed",
            description="Looking for a reliable personal driver for daily commute and errands.",
            company_name="ABC Company",
            job_category="household",
            package_amount=25000.0,
            employment_type="full_time",
            residency_provided=False,
            food_provided=True,
            medical_insurance=True,
            city="Karachi",
            contact_mobile="+************",
            contact_email="<EMAIL>",
            country="Pakistan",
            vehicle_provided=True,
            status="active",
            is_featured=False,
            is_urgent=False,
            view_count=15,
            application_count=3,
            created_at=datetime.now(),
            published_at=datetime.now()
        )
        db.add(job1)
        
        job2 = Job(
            employer_id=employer_user.id,
            title="Delivery Driver - Part Time",
            description="Part-time delivery driver for online food service. Flexible hours.",
            company_name="Food Delivery Co",
            job_category="online_service",
            package_amount=18000.0,
            employment_type="part_time",
            residency_provided=False,
            food_provided=False,
            medical_insurance=False,
            city="Lahore",
            contact_mobile="+************",
            contact_email="<EMAIL>",
            country="Pakistan",
            vehicle_provided=False,
            status="active",
            is_featured=True,
            is_urgent=True,
            view_count=28,
            application_count=7,
            created_at=datetime.now(),
            published_at=datetime.now()
        )
        db.add(job2)
        
        # Create sample news source and category
        print("📰 Creating sample news data...")
        news_source = NewsSource(
            name="Dawn News",
            base_url="https://www.dawn.com",
            rss_url="https://www.dawn.com/feeds/latest-news",
            scraping_enabled=True,
            country="Pakistan",
            language="en",
            is_active=True,
            total_articles_scraped=150,
            successful_scrapes=145,
            failed_scrapes=5,
            created_at=datetime.now()
        )
        db.add(news_source)
        db.flush()
        
        news_category = NewsCategory(
            name="Business",
            slug="business",
            description="Business and economic news",
            is_active=True,
            created_at=datetime.now()
        )
        db.add(news_category)
        db.flush()
        
        # Create sample news article
        news_article = NewsArticle(
            source_id=news_source.id,
            category_id=news_category.id,
            title="Pakistan's Economy Shows Signs of Recovery",
            slug="pakistan-economy-recovery-2024",
            excerpt="Recent indicators suggest positive trends in Pakistan's economic sector...",
            content="Pakistan's economy is showing promising signs of recovery with improved indicators across multiple sectors. The latest data reveals increased industrial production and better employment rates.",
            source_name="Dawn News",
            city="Karachi",
            country="Pakistan",
            status="published",
            is_featured=True,
            view_count=245,
            like_count=18,
            share_count=12,
            published_at=datetime.now() - timedelta(hours=2),
            created_at=datetime.now()
        )
        db.add(news_article)

        # Create sample forums
        print("💬 Creating sample forums...")
        general_forum = Forum(
            name="General Discussion",
            slug="general-discussion",
            description="General chat for drivers and employers",
            forum_type="general",
            city_filter=None,
            allow_voice_messages=True,
            allow_reactions=True,
            allow_tagging=True,
            is_active=True,
            created_at=datetime.now()
        )
        db.add(general_forum)
        db.flush()

        karachi_forum = Forum(
            name="Karachi Drivers",
            slug="karachi-drivers",
            description="Discussion forum for Karachi-based drivers",
            forum_type="city_specific",
            city_filter="Karachi",
            allow_voice_messages=True,
            allow_reactions=True,
            allow_tagging=True,
            is_active=True,
            created_at=datetime.now()
        )
        db.add(karachi_forum)
        db.flush()

        # Create sample forum messages
        sample_message1 = ForumMessage(
            forum_id=general_forum.id,
            user_id=driver_user.id,
            message_text="Hello everyone! New driver here, looking forward to connecting with the community.",
            message_type="text",
            is_approved=True,
            created_at=datetime.now() - timedelta(hours=1)
        )
        db.add(sample_message1)

        sample_message2 = ForumMessage(
            forum_id=karachi_forum.id,
            user_id=driver_user2.id,
            message_text="Traffic is heavy on Shahrah-e-Faisal today. Alternative routes recommended.",
            message_type="text",
            is_approved=True,
            created_at=datetime.now() - timedelta(minutes=30)
        )
        db.add(sample_message2)

        # Commit all changes
        db.commit()
        print("✅ Sample data seeded successfully!")
        print(f"   - Created 3 users (1 employer, 2 drivers)")
        print(f"   - Created 2 driver profiles")
        print(f"   - Created 2 job postings")
        print(f"   - Created 1 news source, category, and article")
        print(f"   - Created 2 forums with sample messages")
        
    except Exception as e:
        print(f"❌ Error seeding data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    try:
        seed_data()
    except Exception as e:
        print(f"❌ Seeding failed: {e}")
        sys.exit(1)
