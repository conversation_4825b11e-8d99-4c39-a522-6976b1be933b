#!/usr/bin/env python3
"""
Comprehensive Backend Testing Script for Drive On API
Tests all endpoints, file uploads, messaging, and Firebase integration
"""

import requests
import json
import os
import time
from typing import Dict, Any, Optional
import base64
from io import BytesIO
try:
    from PIL import Image
except ImportError:
    Image = None

class DriveOnAPITester:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.auth_token = None
        self.admin_token = None
        self.user_id = None
        self.driver_id = None
        self.job_id = None
        self.conversation_id = None
        self.forum_id = None
        self.test_results = []

        # Try to create a test Firebase token for testing
        self.setup_test_auth()

    def setup_test_auth(self):
        """Setup test authentication - try to create a Firebase custom token"""
        try:
            # Try to create a custom Firebase token for testing
            import firebase_admin
            from firebase_admin import auth

            # Create a test user token
            test_uid = "test_user_123"
            custom_token = auth.create_custom_token(test_uid, {
                "email": "<EMAIL>",
                "name": "Test User",
                "admin": False
            })

            # For testing, we'll use this custom token
            # Note: In a real scenario, you'd exchange this for an ID token
            self.auth_token = custom_token.decode('utf-8') if isinstance(custom_token, bytes) else custom_token
            print(f"✅ Created test auth token: {self.auth_token[:20]}...")

        except Exception as e:
            print(f"⚠️ Could not create test auth token: {str(e)}")
            print("   Tests will run without authentication")
            self.auth_token = None

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   Details: {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    def make_request(self, method: str, endpoint: str, data: Any = None, 
                    files: Dict = None, headers: Dict = None, auth_required: bool = True) -> Dict:
        """Make HTTP request with proper headers"""
        url = f"{self.base_url}{endpoint}"
        
        # Default headers
        request_headers = {"Content-Type": "application/json"}
        if headers:
            request_headers.update(headers)
        
        # Add authentication if required and available
        if auth_required and self.auth_token:
            request_headers["Authorization"] = f"Bearer {self.auth_token}"
        
        # Remove Content-Type for file uploads
        if files:
            request_headers.pop("Content-Type", None)
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=request_headers, params=data)
            elif method.upper() == "POST":
                if files:
                    response = requests.post(url, headers=request_headers, data=data, files=files)
                else:
                    response = requests.post(url, headers=request_headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=request_headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=request_headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            return {
                "status_code": response.status_code,
                "data": response.json() if response.content else {},
                "success": 200 <= response.status_code < 300
            }
        except Exception as e:
            return {
                "status_code": 0,
                "data": {"error": str(e)},
                "success": False
            }
    
    def create_test_image(self, filename: str = "test_image.jpg") -> str:
        """Create a test image file"""
        import tempfile
        import os
        # Create a simple test image
        img = Image.new('RGB', (100, 100), color='red')
        # Use tempfile for cross-platform compatibility
        temp_dir = tempfile.gettempdir()
        img_path = os.path.join(temp_dir, filename)
        img.save(img_path, "JPEG")
        return img_path
    
    def create_test_document(self, filename: str = "test_doc.pdf") -> str:
        """Create a test PDF document"""
        # Create a simple test PDF content
        pdf_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF"
        
        # Use tempfile for cross-platform compatibility
        import tempfile
        import os
        temp_dir = tempfile.gettempdir()
        doc_path = os.path.join(temp_dir, filename)
        with open(doc_path, 'wb') as f:
            f.write(pdf_content)
        return doc_path
    
    def test_health_check(self):
        """Test health check endpoint"""
        # Test the root endpoint first
        response = self.make_request("GET", "/", auth_required=False)
        success = response["status_code"] == 200
        self.log_test("Health Check", success, f"Status: {response['status_code']}")

        if success and "data" in response:
            print(f"   API Version: {response['data'].get('version', 'Unknown')}")
            print(f"   Status: {response['data'].get('status', 'Unknown')}")
    
    def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")
        
        # Note: These tests require actual Firebase tokens
        # For demo purposes, we'll test the endpoint structure
        
        # Test token verification endpoint
        response = self.make_request("POST", "/api/v1/auth/verify-token", 
                                   {"token": "dummy_token"}, auth_required=False)
        self.log_test("Token Verification Endpoint", 
                     response["status_code"] in [401, 400], 
                     "Expected 401/400 for invalid token")
        
        # Test get current user (should fail without token)
        response = self.make_request("GET", "/api/v1/auth/me")
        self.log_test("Get Current User (No Auth)", 
                     response["status_code"] == 401, 
                     "Expected 401 for missing auth")
    
    def test_driver_endpoints(self):
        """Test driver management endpoints"""
        print("\n🚗 Testing Driver Endpoints...")
        
        # Test get drivers (public endpoint)
        response = self.make_request("GET", "/api/v1/drivers/", auth_required=False)
        success = response["success"] or response["status_code"] in [200, 401]
        self.log_test("Get Drivers List", success, f"Status: {response['status_code']}")
        
        # Test driver search
        response = self.make_request("GET", "/api/v1/drivers/search", 
                                   {"q": "test"}, auth_required=False)
        success = response["status_code"] in [200, 401, 422]  # 422 for validation error
        self.log_test("Driver Search", success, f"Status: {response['status_code']}")
        
        # Test get specific driver
        response = self.make_request("GET", "/api/v1/drivers/1", auth_required=False)
        success = response["status_code"] in [200, 404, 401]
        self.log_test("Get Specific Driver", success, f"Status: {response['status_code']}")
        
        # Test driver registration (requires auth)
        driver_registration_data = {
            "name": "Test Driver",
            "father_name": "Test Father",
            "mobile_number": "+923001234567",
            "education": "bachelors",
            "experience_years": 5,
            "marital_status": "single",
            "city_of_priority": "Karachi",
            "bio": "Experienced driver with clean record",
            "profile_picture_url": "https://example.com/profile.jpg",
            "cnic_front_url": "https://example.com/cnic_front.jpg",
            "cnic_back_url": "https://example.com/cnic_back.jpg",
            "driving_license_url": "https://example.com/license.jpg",
            "electricity_bill_url": "https://example.com/bill.jpg",
            "police_certificate_url": "https://example.com/certificate.jpg"
        }
        
        response = self.make_request("POST", "/api/v1/drivers/register", driver_registration_data)
        success = response["status_code"] in [200, 201, 401, 400]
        self.log_test("Driver Registration", success, f"Status: {response['status_code']}")
    
    def test_job_endpoints(self):
        """Test job management endpoints"""
        print("\n💼 Testing Job Endpoints...")
        
        # Test get jobs
        response = self.make_request("GET", "/api/v1/jobs/", auth_required=False)
        success = response["success"] or response["status_code"] in [200, 401]
        self.log_test("Get Jobs List", success, f"Status: {response['status_code']}")
        
        # Test job creation (requires auth)
        job_data = {
            "title": "Personal Driver Needed",
            "description": "Looking for a reliable driver for family use",
            "job_category": "personal_driver",
            "employment_type": "part_time",
            "location": "Karachi",
            "city": "Karachi",
            "state": "Sindh",
            "country": "Pakistan",
            "salary_min": 30000,
            "salary_max": 50000,
            "salary_type": "monthly",
            "requirements": ["Valid driving license", "Clean record"],
            "benefits": ["Fuel allowance", "Flexible hours"],
            "contact_phone": "+923001234567",
            "contact_email": "<EMAIL>"
        }
        
        response = self.make_request("POST", "/api/v1/jobs/", job_data)
        success = response["status_code"] in [200, 201, 401, 400]
        self.log_test("Job Creation", success, f"Status: {response['status_code']}")
        
        # Test get specific job
        response = self.make_request("GET", "/api/v1/jobs/1", auth_required=False)
        success = response["status_code"] in [200, 404, 401]
        self.log_test("Get Specific Job", success, f"Status: {response['status_code']}")
    
    def test_application_endpoints(self):
        """Test job application endpoints"""
        print("\n📝 Testing Application Endpoints...")
        
        # Test job application (requires auth)
        application_data = {
            "job_id": 1,
            "cover_letter": "I am interested in this position and have relevant experience.",
            "expected_salary": 40000,
            "availability": "immediate",
            "additional_info": "Available for interview anytime"
        }
        
        response = self.make_request("POST", "/api/v1/applications/", application_data)
        success = response["status_code"] in [200, 201, 401, 400, 404]
        self.log_test("Job Application", success, f"Status: {response['status_code']}")
        
        # Test get my applications
        response = self.make_request("GET", "/api/v1/applications/my-applications")
        success = response["status_code"] in [200, 401]
        self.log_test("Get My Applications", success, f"Status: {response['status_code']}")
    
    def test_upload_endpoints(self):
        """Test file upload endpoints"""
        print("\n📁 Testing Upload Endpoints...")
        
        # Create test files
        test_image = self.create_test_image()
        test_doc = self.create_test_document()
        
        try:
            # Test profile image upload
            with open(test_image, 'rb') as f:
                files = {'file': ('test_profile.jpg', f, 'image/jpeg')}
                response = self.make_request("POST", "/api/v1/uploads/profile-image", 
                                           files=files, auth_required=True)
                success = response["status_code"] in [200, 201, 401, 400]
                self.log_test("Profile Image Upload", success, f"Status: {response['status_code']}")
            
            # Test document upload
            with open(test_doc, 'rb') as f:
                files = {'file': ('test_license.pdf', f, 'application/pdf')}
                data = {'document_type': 'driving_license'}
                response = self.make_request("POST", "/api/v1/uploads/document", 
                                           data=data, files=files, auth_required=True)
                success = response["status_code"] in [200, 201, 401, 400]
                self.log_test("Document Upload", success, f"Status: {response['status_code']}")
            
            # Test vehicle image upload
            with open(test_image, 'rb') as f:
                files = {'file': ('test_vehicle.jpg', f, 'image/jpeg')}
                response = self.make_request("POST", "/api/v1/uploads/vehicle-image", 
                                           files=files, auth_required=True)
                success = response["status_code"] in [200, 201, 401, 400]
                self.log_test("Vehicle Image Upload", success, f"Status: {response['status_code']}")
        
        finally:
            # Clean up test files
            try:
                os.remove(test_image)
                os.remove(test_doc)
            except:
                pass
    
    def test_messaging_endpoints(self):
        """Test messaging system endpoints"""
        print("\n💬 Testing Messaging Endpoints...")
        
        # Test get conversations
        response = self.make_request("GET", "/api/v1/messaging/conversations")
        success = response["status_code"] in [200, 401]
        self.log_test("Get Conversations", success, f"Status: {response['status_code']}")
        
        # Test contact driver
        contact_data = {
            "driver_id": 1,
            "job_id": 1,
            "message": "Hi, I'm interested in hiring you for this job.",
            "include_job_details": True
        }
        
        response = self.make_request("POST", "/api/v1/messaging/contact-driver", contact_data)
        success = response["status_code"] in [200, 201, 401, 400, 404]
        self.log_test("Contact Driver", success, f"Status: {response['status_code']}")
        
        # Test get conversation messages
        response = self.make_request("GET", "/api/v1/messaging/conversations/1/messages")
        success = response["status_code"] in [200, 401, 404]
        self.log_test("Get Conversation Messages", success, f"Status: {response['status_code']}")
    
    def test_forum_endpoints(self):
        """Test forum endpoints"""
        print("\n🗣️ Testing Forum Endpoints...")
        
        # Test get forums
        response = self.make_request("GET", "/api/v1/forum/")
        success = response["status_code"] in [200, 401]
        self.log_test("Get Forums", success, f"Status: {response['status_code']}")
        
        # Test get forum messages
        response = self.make_request("GET", "/api/v1/forum/1/messages")
        success = response["status_code"] in [200, 401, 404]
        self.log_test("Get Forum Messages", success, f"Status: {response['status_code']}")
        
        # Test send forum message
        message_data = {
            "message_text": "Hello everyone! This is a test message.",
            "message_type": "text"
        }
        
        response = self.make_request("POST", "/api/v1/forum/1/messages", message_data)
        success = response["status_code"] in [200, 201, 401, 400, 404]
        self.log_test("Send Forum Message", success, f"Status: {response['status_code']}")
    
    def test_news_endpoints(self):
        """Test news endpoints"""
        print("\n📰 Testing News Endpoints...")
        
        # Test get news articles
        response = self.make_request("GET", "/api/v1/news/", auth_required=False)
        success = response["success"] or response["status_code"] in [200, 401]
        self.log_test("Get News Articles", success, f"Status: {response['status_code']}")
        
        # Test get specific news article
        response = self.make_request("GET", "/api/v1/news/1", auth_required=False)
        success = response["status_code"] in [200, 404, 401]
        self.log_test("Get Specific News Article", success, f"Status: {response['status_code']}")
        
        # Test get news categories
        response = self.make_request("GET", "/api/v1/news/categories", auth_required=False)
        success = response["status_code"] in [200, 401]
        self.log_test("Get News Categories", success, f"Status: {response['status_code']}")
    
    def test_notification_endpoints(self):
        """Test notification endpoints"""
        print("\n🔔 Testing Notification Endpoints...")
        
        # Test get notifications
        response = self.make_request("GET", "/api/v1/notifications/")
        success = response["status_code"] in [200, 401]
        self.log_test("Get Notifications", success, f"Status: {response['status_code']}")
        
        # Test mark notification as read
        response = self.make_request("PUT", "/api/v1/notifications/1/read")
        success = response["status_code"] in [200, 401, 404]
        self.log_test("Mark Notification Read", success, f"Status: {response['status_code']}")
    
    def test_admin_endpoints(self):
        """Test admin endpoints"""
        print("\n👑 Testing Admin Endpoints...")
        
        # Test get pending driver applications (admin only)
        response = self.make_request("GET", "/api/v1/drivers/pending-applications")
        success = response["status_code"] in [200, 401, 403]
        self.log_test("Get Pending Driver Applications", success, f"Status: {response['status_code']}")
        
        # Test admin dashboard stats
        response = self.make_request("GET", "/api/v1/admin/dashboard")
        success = response["status_code"] in [200, 401, 403, 404]
        self.log_test("Admin Dashboard", success, f"Status: {response['status_code']}")
    
    def test_database_connection(self):
        """Test database connectivity through API"""
        print("\n🗄️ Testing Database Connection...")
        
        # Test any endpoint that requires database access
        response = self.make_request("GET", "/api/v1/drivers/", auth_required=False)
        success = response["status_code"] != 500  # 500 would indicate database error
        self.log_test("Database Connection", success, 
                     "Database accessible" if success else "Database connection failed")
    
    def test_firebase_integration(self):
        """Test Firebase integration"""
        print("\n🔥 Testing Firebase Integration...")
        
        # Test Firebase-dependent endpoints
        # Profile image upload tests Firebase Storage
        test_image = self.create_test_image()
        
        try:
            with open(test_image, 'rb') as f:
                files = {'file': ('firebase_test.jpg', f, 'image/jpeg')}
                response = self.make_request("POST", "/api/v1/uploads/profile-image", 
                                           files=files, auth_required=True)
                # 401 means Firebase auth is working (rejecting invalid token)
                # 500 would mean Firebase is not configured
                success = response["status_code"] in [200, 201, 401, 400]
                self.log_test("Firebase Storage Integration", success, 
                             f"Status: {response['status_code']}")
        finally:
            try:
                os.remove(test_image)
            except:
                pass
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Comprehensive Backend Testing...")
        print(f"Testing API at: {self.base_url}")
        print("=" * 60)
        
        # Run all test categories
        self.test_health_check()
        self.test_database_connection()
        self.test_firebase_integration()
        self.test_authentication_endpoints()
        self.test_driver_endpoints()
        self.test_job_endpoints()
        self.test_application_endpoints()
        self.test_upload_endpoints()
        self.test_messaging_endpoints()
        self.test_forum_endpoints()
        self.test_news_endpoints()
        self.test_notification_endpoints()
        self.test_admin_endpoints()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['details']}")
        
        print("\n📝 Notes:")
        print("   - Many tests expect 401 (Unauthorized) responses due to missing Firebase tokens")
        print("   - This is normal and indicates the authentication system is working")
        print("   - 500 errors indicate server/configuration issues that need attention")
        print("   - 404 errors for specific resources are expected when testing with dummy data")


def main():
    """Main function to run tests"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Drive On Backend API")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API (default: http://localhost:8000)")
    parser.add_argument("--token", help="Firebase authentication token for authenticated tests")
    
    args = parser.parse_args()
    
    # Create tester instance
    tester = DriveOnAPITester(args.url)
    
    # Set auth token if provided
    if args.token:
        tester.auth_token = args.token
        print(f"🔑 Using provided authentication token")
    
    # Run all tests
    tester.run_all_tests()


if __name__ == "__main__":
    main()
