#!/usr/bin/env python3
"""
Migration Script: SQLite to Firestore
Migrates existing SQLite data to Firebase Firestore
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.core.database import engine
from app.core.firebase import initialize_firebase
from app.models.user import User, Driver
from app.models.job import Job
from app.models.news import NewsArticle, NewsSource, NewsCategory
from app.models.forum import Forum, ForumMessage
from app.services.firestore_service import (
    user_service, driver_service, job_service, 
    forum_service, news_service
)


async def migrate_users():
    """Migrate users from SQLite to Firestore"""
    print("👥 Migrating users...")
    
    db = Session(bind=engine)
    try:
        users = db.query(User).all()
        migrated_count = 0
        
        for user in users:
            user_data = {
                'firebase_uid': user.firebase_uid,
                'email': user.email,
                'full_name': user.full_name,
                'phone': user.phone,
                'profile_image_url': user.profile_image_url,
                'user_type': user.user_type,
                'is_active': user.is_active,
                'is_verified': user.is_verified,
                'created_at': user.created_at or datetime.now(),
                'updated_at': user.updated_at,
                'last_login': user.last_login
            }
            
            try:
                await user_service.create_user(user_data)
                migrated_count += 1
                print(f"   ✅ Migrated user: {user.email}")
            except Exception as e:
                print(f"   ❌ Failed to migrate user {user.email}: {e}")
        
        print(f"   📊 Migrated {migrated_count}/{len(users)} users")
        
    except Exception as e:
        print(f"❌ Error migrating users: {e}")
    finally:
        db.close()


async def migrate_drivers():
    """Migrate drivers from SQLite to Firestore"""
    print("🚗 Migrating drivers...")
    
    db = Session(bind=engine)
    try:
        drivers = db.query(Driver).all()
        migrated_count = 0
        
        for driver in drivers:
            driver_data = {
                'user_id': driver.user_id,
                'name': driver.name,
                'father_name': driver.father_name,
                'mobile_number': driver.mobile_number,
                'education': driver.education,
                'experience_years': driver.experience_years,
                'marital_status': driver.marital_status,
                'city_of_priority': driver.city_of_priority,
                'city': driver.city,
                'country': driver.country or 'Pakistan',
                'is_available': driver.is_available,
                'application_status': driver.application_status,
                'background_check_status': driver.background_check_status,
                'license_verified': driver.license_verified,
                'documents_verified': driver.documents_verified,
                'insurance_verified': getattr(driver, 'insurance_verified', False),
                'cnic_front_url': driver.cnic_front_url,
                'cnic_back_url': driver.cnic_back_url,
                'driving_license_url': driver.driving_license_url,
                'electricity_bill_url': driver.electricity_bill_url,
                'police_certificate_url': driver.police_certificate_url,
                'bio': driver.bio,
                'total_jobs_completed': driver.total_jobs_completed or 0,
                'average_rating': driver.average_rating or 0.0,
                'total_ratings': driver.total_ratings or 0,
                'created_at': driver.created_at or datetime.now(),
                'updated_at': driver.updated_at,
                'approved_at': driver.approved_at
            }
            
            try:
                await driver_service.create_driver(driver_data)
                migrated_count += 1
                print(f"   ✅ Migrated driver: {driver.name}")
            except Exception as e:
                print(f"   ❌ Failed to migrate driver {driver.name}: {e}")
        
        print(f"   📊 Migrated {migrated_count}/{len(drivers)} drivers")
        
    except Exception as e:
        print(f"❌ Error migrating drivers: {e}")
    finally:
        db.close()


async def migrate_jobs():
    """Migrate jobs from SQLite to Firestore"""
    print("💼 Migrating jobs...")
    
    db = Session(bind=engine)
    try:
        jobs = db.query(Job).all()
        migrated_count = 0
        
        for job in jobs:
            job_data = {
                'employer_id': str(job.employer_id),
                'title': job.title,
                'description': job.description,
                'company_name': job.company_name,
                'job_category': job.job_category,
                'package_amount': job.package_amount,
                'currency': job.currency or 'PKR',
                'employment_type': job.employment_type,
                'residency_provided': job.residency_provided or False,
                'food_provided': job.food_provided or False,
                'medical_insurance': job.medical_insurance or False,
                'city': job.city,
                'contact_mobile': job.contact_mobile,
                'contact_email': job.contact_email,
                'country': job.country or 'Pakistan',
                'vehicle_provided': job.vehicle_provided or False,
                'status': job.status or 'active',
                'is_featured': job.is_featured or False,
                'is_urgent': job.is_urgent or False,
                'view_count': job.view_count or 0,
                'application_count': job.application_count or 0,
                'created_at': job.created_at or datetime.now(),
                'updated_at': job.updated_at,
                'published_at': job.published_at or datetime.now()
            }
            
            try:
                await job_service.create_job(job_data)
                migrated_count += 1
                print(f"   ✅ Migrated job: {job.title}")
            except Exception as e:
                print(f"   ❌ Failed to migrate job {job.title}: {e}")
        
        print(f"   📊 Migrated {migrated_count}/{len(jobs)} jobs")
        
    except Exception as e:
        print(f"❌ Error migrating jobs: {e}")
    finally:
        db.close()


async def migrate_forums():
    """Migrate forums and messages from SQLite to Firestore"""
    print("💬 Migrating forums...")
    
    db = Session(bind=engine)
    try:
        forums = db.query(Forum).all()
        migrated_forums = 0
        migrated_messages = 0
        
        for forum in forums:
            forum_data = {
                'name': forum.name,
                'slug': forum.slug,
                'description': forum.description,
                'forum_type': forum.forum_type,
                'city_filter': forum.city_filter,
                'allow_voice_messages': forum.allow_voice_messages or True,
                'allow_reactions': forum.allow_reactions or True,
                'allow_tagging': forum.allow_tagging or True,
                'is_active': forum.is_active,
                'created_at': forum.created_at or datetime.now()
            }
            
            try:
                forum_id = await forum_service.create_forum(forum_data)
                migrated_forums += 1
                print(f"   ✅ Migrated forum: {forum.name}")
                
                # Migrate messages for this forum
                messages = db.query(ForumMessage).filter(ForumMessage.forum_id == forum.id).all()
                
                for message in messages:
                    message_data = {
                        'forum_id': forum_id,
                        'user_id': str(message.user_id),
                        'message_text': message.message_text,
                        'message_type': message.message_type or 'text',
                        'voice_message_url': message.voice_message_url,
                        'voice_duration': message.voice_duration,
                        'is_approved': message.is_approved,
                        'created_at': message.created_at or datetime.now()
                    }
                    
                    try:
                        await forum_service.send_message(message_data)
                        migrated_messages += 1
                        print(f"     ✅ Migrated message in {forum.name}")
                    except Exception as e:
                        print(f"     ❌ Failed to migrate message: {e}")
                        
            except Exception as e:
                print(f"   ❌ Failed to migrate forum {forum.name}: {e}")
        
        print(f"   📊 Migrated {migrated_forums} forums and {migrated_messages} messages")
        
    except Exception as e:
        print(f"❌ Error migrating forums: {e}")
    finally:
        db.close()


async def migrate_news():
    """Migrate news articles from SQLite to Firestore"""
    print("📰 Migrating news articles...")
    
    db = Session(bind=engine)
    try:
        articles = db.query(NewsArticle).all()
        migrated_count = 0
        
        for article in articles:
            article_data = {
                'title': article.title,
                'slug': article.slug,
                'excerpt': article.excerpt,
                'content': article.content,
                'source_name': article.source_name,
                'city': article.city,
                'country': article.country or 'Pakistan',
                'status': article.status or 'published',
                'is_featured': article.is_featured or False,
                'view_count': article.view_count or 0,
                'like_count': article.like_count or 0,
                'share_count': article.share_count or 0,
                'created_at': article.created_at or datetime.now(),
                'published_at': article.published_at or datetime.now()
            }
            
            try:
                await news_service.create_article(article_data)
                migrated_count += 1
                print(f"   ✅ Migrated article: {article.title}")
            except Exception as e:
                print(f"   ❌ Failed to migrate article {article.title}: {e}")
        
        print(f"   📊 Migrated {migrated_count}/{len(articles)} articles")
        
    except Exception as e:
        print(f"❌ Error migrating news: {e}")
    finally:
        db.close()


async def setup_firestore_collections():
    """Set up initial Firestore collections and indexes"""
    print("🔧 Setting up Firestore collections...")
    
    try:
        # Create default forums if they don't exist
        forums = await forum_service.get_forums()
        
        if not forums:
            print("   📝 Creating default forums...")
            
            # General Discussion Forum
            general_forum_data = {
                'name': 'General Discussion',
                'slug': 'general-discussion',
                'description': 'General chat for drivers and employers',
                'forum_type': 'general',
                'city_filter': None,
                'allow_voice_messages': True,
                'allow_reactions': True,
                'allow_tagging': True
            }
            await forum_service.create_forum(general_forum_data)
            print("     ✅ Created General Discussion forum")
            
            # City-specific forums
            cities = ['Karachi', 'Lahore', 'Islamabad', 'Rawalpindi', 'Faisalabad']
            for city in cities:
                city_forum_data = {
                    'name': f'{city} Drivers',
                    'slug': f'{city.lower()}-drivers',
                    'description': f'Discussion forum for {city}-based drivers',
                    'forum_type': 'city_specific',
                    'city_filter': city,
                    'allow_voice_messages': True,
                    'allow_reactions': True,
                    'allow_tagging': True
                }
                await forum_service.create_forum(city_forum_data)
                print(f"     ✅ Created {city} forum")
        
        print("   ✅ Firestore collections setup complete")
        
    except Exception as e:
        print(f"❌ Error setting up Firestore collections: {e}")


async def main():
    """Main migration function"""
    print("🚀 Starting migration from SQLite to Firestore...")
    print("=" * 50)
    
    try:
        # Initialize Firebase
        initialize_firebase()
        print("✅ Firebase initialized")
        
        # Run migrations
        await migrate_users()
        await migrate_drivers()
        await migrate_jobs()
        await migrate_forums()
        await migrate_news()
        await setup_firestore_collections()
        
        print("=" * 50)
        print("✅ Migration completed successfully!")
        print("🔥 Your data is now stored in Firebase Firestore")
        print("📱 Real-time features are now available")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
