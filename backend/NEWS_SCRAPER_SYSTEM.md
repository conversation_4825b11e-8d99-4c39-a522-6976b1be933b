# Comprehensive News Scraper System

## Overview
Implemented a complete web scraping system that automatically fetches news about petrol prices, car prices, and automobile industry from major Pakistani news sources every 2 hours.

## 🎯 **Key Features Implemented**

### 1. **Multi-Source News Scraping**
- ✅ **7 Major Pakistani News Sources**: Dawn, The News, Express Tribune, Geo News, ARY News, Business Recorder, Daily Times
- ✅ **Dual Scraping Methods**: RSS feeds + Web scraping with CSS selectors
- ✅ **Automatic Fallback**: If RSS fails, falls back to web scraping
- ✅ **Smart Deduplication**: Prevents duplicate articles using URL matching

### 2. **Automated Categorization**
- ✅ **Petrol Prices**: Fuel prices, diesel, CNG, oil subsidies
- ✅ **Car Prices**: Vehicle prices, car launches, market trends
- ✅ **Automobile Industry**: Manufacturing, sales, policies, imports
- ✅ **Traffic Updates**: Road conditions, traffic alerts
- ✅ **General News**: Other relevant news

### 3. **Geographic Coverage**
- ✅ **All Major Pakistani Cities**: Karachi, Lahore, Islamabad, Rawalpindi, Faisalabad, Multan, Peshawar, Quetta, etc.
- ✅ **Province-wise Filtering**: Sindh, Punjab, KPK, Balochistan, ICT
- ✅ **Location Extraction**: Automatically detects cities mentioned in articles

### 4. **Automated Scheduling**
- ✅ **Every 2 Hours**: Automatic scraping of all active sources
- ✅ **Background Processing**: Non-blocking scheduled tasks
- ✅ **Smart Frequency**: Respects source-specific scraping intervals
- ✅ **Error Recovery**: Automatic retry on failures

### 5. **Advanced Content Processing**
- ✅ **Price Extraction**: Automatically detects price information
- ✅ **Content Analysis**: Relevance scoring and sentiment analysis
- ✅ **Keyword Extraction**: Automatic tagging and categorization
- ✅ **Content Cleaning**: Removes ads, navigation, and irrelevant content

## 📊 **Database Schema**

### **News Sources Table**
```sql
CREATE TABLE news_sources (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    base_url VARCHAR(500) NOT NULL,
    rss_url VARCHAR(500),
    scraping_enabled BOOLEAN DEFAULT TRUE,
    scraping_frequency INTEGER DEFAULT 2, -- Hours
    last_scraped_at TIMESTAMP,
    next_scrape_at TIMESTAMP,
    country VARCHAR(50) DEFAULT 'Pakistan',
    language VARCHAR(10) DEFAULT 'en',
    reliability_score FLOAT DEFAULT 0.8,
    selectors JSON, -- CSS selectors for web scraping
    categories VARCHAR(500), -- Comma-separated categories
    total_articles_scraped INTEGER DEFAULT 0,
    successful_scrapes INTEGER DEFAULT 0,
    failed_scrapes INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### **News Articles Table**
```sql
CREATE TABLE news_articles (
    id SERIAL PRIMARY KEY,
    source_id INTEGER REFERENCES news_sources(id),
    category_id INTEGER REFERENCES news_categories(id),
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) UNIQUE NOT NULL,
    excerpt TEXT,
    content TEXT NOT NULL,
    summary TEXT,
    original_url VARCHAR(1000) UNIQUE, -- Prevent duplicates
    source_name VARCHAR(100),
    author_name VARCHAR(100),
    city VARCHAR(100),
    province VARCHAR(50),
    country VARCHAR(50) DEFAULT 'Pakistan',
    featured_image_url VARCHAR(1000),
    images JSON, -- Multiple images
    video_url VARCHAR(1000),
    read_time INTEGER DEFAULT 0,
    word_count INTEGER DEFAULT 0,
    price_data JSON, -- Extracted price information
    price_change FLOAT,
    price_change_percentage FLOAT,
    tags VARCHAR(1000),
    keywords JSON,
    sentiment_score FLOAT, -- -1.0 to 1.0
    relevance_score FLOAT DEFAULT 0.5, -- 0.0 to 1.0
    is_featured BOOLEAN DEFAULT FALSE,
    is_breaking BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    is_scraped BOOLEAN DEFAULT TRUE,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    share_count INTEGER DEFAULT 0,
    scraped_at TIMESTAMP DEFAULT NOW(),
    published_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### **Scraping Jobs Table**
```sql
CREATE TABLE scraping_jobs (
    id SERIAL PRIMARY KEY,
    source_id INTEGER REFERENCES news_sources(id),
    job_type VARCHAR(50) DEFAULT 'scheduled',
    status VARCHAR(20) DEFAULT 'pending',
    articles_found INTEGER DEFAULT 0,
    articles_created INTEGER DEFAULT 0,
    articles_updated INTEGER DEFAULT 0,
    articles_skipped INTEGER DEFAULT 0,
    error_message TEXT,
    execution_time FLOAT, -- Seconds
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔍 **Configured News Sources**

### **1. Dawn News**
- **URL**: https://www.dawn.com
- **RSS**: https://www.dawn.com/feeds/home
- **Categories**: Petrol prices, car prices, automobile industry, general
- **Reliability**: 95%

### **2. The News International**
- **URL**: https://www.thenews.com.pk
- **RSS**: https://www.thenews.com.pk/rss/1/1
- **Categories**: Petrol prices, car prices, automobile industry, general
- **Reliability**: 90%

### **3. Express Tribune**
- **URL**: https://tribune.com.pk
- **RSS**: https://tribune.com.pk/feed/home
- **Categories**: Petrol prices, car prices, automobile industry, general
- **Reliability**: 88%

### **4. Geo News**
- **URL**: https://www.geo.tv
- **RSS**: https://www.geo.tv/rss/1/1
- **Categories**: Petrol prices, car prices, automobile industry, general
- **Reliability**: 85%

### **5. ARY News**
- **URL**: https://arynews.tv
- **RSS**: https://arynews.tv/feed/
- **Categories**: Petrol prices, car prices, automobile industry, general
- **Reliability**: 82%

### **6. Business Recorder**
- **URL**: https://www.brecorder.com
- **RSS**: https://www.brecorder.com/feeds/latest
- **Categories**: Petrol prices, car prices, automobile industry (specialized)
- **Reliability**: 90%

### **7. Daily Times**
- **URL**: https://dailytimes.com.pk
- **RSS**: https://dailytimes.com.pk/feed/
- **Categories**: Petrol prices, car prices, automobile industry, general
- **Reliability**: 80%

## 🚀 **API Endpoints**

### **Get News Articles**
```bash
GET /api/v1/news/?category=petrol-prices&city=Karachi&page=1&limit=20

# Response
{
  "success": true,
  "message": "News articles retrieved successfully",
  "data": {
    "articles": [
      {
        "id": 123,
        "title": "Petrol Price Increased by Rs. 5 in Karachi",
        "slug": "petrol-price-increased-rs-5-karachi",
        "excerpt": "The government has announced a Rs. 5 increase in petrol prices...",
        "featured_image_url": "https://example.com/image.jpg",
        "source_name": "Dawn News",
        "city": "Karachi",
        "category_name": "Petrol Prices",
        "is_featured": false,
        "is_breaking": true,
        "view_count": 150,
        "published_at": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-15T10:35:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "pages": 3
    }
  }
}
```

### **Get Single Article**
```bash
GET /api/v1/news/123

# Response
{
  "success": true,
  "message": "News article retrieved successfully",
  "data": {
    "article": {
      "id": 123,
      "title": "Petrol Price Increased by Rs. 5 in Karachi",
      "content": "Full article content here...",
      "price_data": [
        {
          "item_name": "Petrol",
          "current_price": 280.50,
          "previous_price": 275.50,
          "price_change": 5.00,
          "price_change_percentage": 1.8,
          "currency": "PKR",
          "unit": "per liter"
        }
      ],
      "city": "Karachi",
      "province": "Sindh",
      "keywords": ["petrol", "fuel", "price", "increase"],
      "sentiment_score": -0.3,
      "relevance_score": 0.9,
      "view_count": 151
    }
  }
}
```

### **Start Manual Scraping**
```bash
POST /api/v1/news/scrape
Authorization: Bearer <firebase_token>

# Optional: Specify source IDs
POST /api/v1/news/scrape?source_ids=1,2,3

# Response
{
  "success": true,
  "message": "Scraping started successfully",
  "data": {
    "sources_queued": 7,
    "source_names": ["Dawn News", "The News", "Express Tribune", ...],
    "estimated_completion": "5-10 minutes"
  }
}
```

### **Get Scraping Jobs Status**
```bash
GET /api/v1/news/scraping/jobs?status_filter=running

# Response
{
  "success": true,
  "message": "Scraping jobs retrieved successfully",
  "data": {
    "jobs": [
      {
        "id": 456,
        "source_name": "Dawn News",
        "status": "completed",
        "articles_found": 25,
        "articles_created": 8,
        "articles_updated": 2,
        "articles_skipped": 15,
        "execution_time": 45.2,
        "started_at": "2024-01-15T10:00:00Z",
        "completed_at": "2024-01-15T10:00:45Z"
      }
    ]
  }
}
```

### **Get News Statistics**
```bash
GET /api/v1/news/stats

# Response
{
  "success": true,
  "message": "News statistics retrieved successfully",
  "data": {
    "total_articles": 1250,
    "articles_today": 45,
    "articles_this_week": 320,
    "articles_this_month": 1100,
    "total_sources": 7,
    "active_sources": 7,
    "categories_stats": [
      {"category": "Petrol Prices", "article_count": 450},
      {"category": "Car Prices", "article_count": 380},
      {"category": "Automobile Industry", "article_count": 220}
    ],
    "top_cities": [
      {"city": "Karachi", "article_count": 280},
      {"city": "Lahore", "article_count": 245},
      {"city": "Islamabad", "article_count": 180}
    ]
  }
}
```

## ⚙️ **Automated Scheduling**

### **Scheduler Configuration**
- **Main Scraping**: Every 2 hours (configurable per source)
- **Daily Cleanup**: 2:00 AM (removes old jobs and logs)
- **Weekly Statistics**: Sunday 1:00 AM (generates reports)
- **Background Processing**: Non-blocking, runs in separate thread

### **Scheduler Features**
- ✅ **Smart Frequency**: Respects individual source frequencies
- ✅ **Error Recovery**: Automatic retry on failures (max 3 attempts)
- ✅ **Performance Monitoring**: Tracks execution time and memory usage
- ✅ **Breaking News Detection**: Identifies and prioritizes urgent news
- ✅ **Cleanup Tasks**: Removes old data to maintain performance

## 🔧 **Setup Instructions**

### **1. Install Dependencies**
```bash
cd backend
pip install -r requirements.txt
```

### **2. Initialize News System**
```bash
python initialize_news_scraper.py
```

### **3. Manual Scraping Test**
```bash
# Test single source
curl -X POST "http://localhost:8000/api/v1/news/scrape?source_ids=1" \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN"

# Test all sources
curl -X POST "http://localhost:8000/api/v1/news/scrape" \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN"
```

### **4. View Results**
```bash
# Get latest articles
curl "http://localhost:8000/api/v1/news/?limit=10&sort_by=created_at&sort_order=desc"

# Get petrol price news
curl "http://localhost:8000/api/v1/news/?category=petrol-prices"

# Get Karachi news
curl "http://localhost:8000/api/v1/news/?city=Karachi"
```

## 📈 **Content Processing Features**

### **1. Automatic Categorization**
- **Keyword Matching**: Uses predefined keywords for each category
- **Priority System**: Higher priority categories checked first
- **Fallback**: Defaults to "General News" if no category matches

### **2. Price Extraction**
- **Multiple Formats**: Rs. 280, PKR 280, 280 rupees
- **Price Changes**: Calculates increases/decreases
- **Percentage Changes**: Automatic calculation
- **Historical Tracking**: Stores price trends

### **3. Location Detection**
- **City Recognition**: Identifies 30+ Pakistani cities
- **Province Mapping**: Maps cities to correct provinces
- **Geographic Filtering**: Enables location-based news filtering

### **4. Content Quality**
- **Relevance Scoring**: 0.0 to 1.0 based on keywords and category match
- **Sentiment Analysis**: -1.0 (negative) to 1.0 (positive)
- **Duplicate Prevention**: URL-based deduplication
- **Content Cleaning**: Removes ads and navigation elements

## 🎯 **Targeted Keywords**

### **Petrol Prices**
- petrol price, fuel price, diesel price, gas price, oil price
- petroleum, fuel cost, petrol rate, diesel rate, CNG price
- fuel subsidy, oil subsidy, petrol increase, fuel hike

### **Car Prices**
- car price, vehicle price, auto price, car cost, vehicle cost
- honda price, toyota price, suzuki price, hyundai price
- car launch, new car, vehicle launch, auto launch
- car market, auto market, vehicle market

### **Automobile Industry**
- automobile industry, auto industry, car industry, vehicle industry
- auto sector, automotive sector, car manufacturing, auto assembly
- car production, vehicle production, auto sales, car sales
- auto policy, automotive policy, car import, vehicle import

## 🚨 **Error Handling & Monitoring**

### **Robust Error Recovery**
- **Connection Timeouts**: 30-second timeout per request
- **Retry Logic**: Up to 3 retries with exponential backoff
- **Graceful Degradation**: Continues with other sources if one fails
- **Error Logging**: Detailed logs for debugging

### **Performance Monitoring**
- **Execution Time**: Tracks scraping duration per source
- **Memory Usage**: Monitors resource consumption
- **Success Rates**: Calculates reliability scores
- **Article Metrics**: Tracks found/created/skipped articles

### **Data Quality Assurance**
- **Content Validation**: Minimum length requirements
- **URL Validation**: Prevents invalid article URLs
- **Image Validation**: Checks image accessibility
- **Duplicate Detection**: Prevents duplicate content

## 🎉 **Benefits for Drive On Platform**

### **1. Real-Time Updates**
- **Fresh Content**: New articles every 2 hours
- **Breaking News**: Immediate detection of urgent updates
- **Price Alerts**: Automatic price change notifications
- **Market Trends**: Track automotive industry developments

### **2. Comprehensive Coverage**
- **Multiple Sources**: 7 major Pakistani news outlets
- **Geographic Coverage**: All major cities and provinces
- **Category Diversity**: Petrol, cars, industry, traffic, general
- **Language Support**: English content with Urdu support planned

### **3. User Engagement**
- **Personalized News**: Filter by city and interests
- **Price Tracking**: Monitor fuel and vehicle prices
- **Industry Insights**: Stay updated on automotive sector
- **Local Relevance**: City-specific traffic and price updates

### **4. Business Intelligence**
- **Market Analysis**: Track price trends and patterns
- **Consumer Insights**: Understand market sentiment
- **Competitive Intelligence**: Monitor industry developments
- **Data-Driven Decisions**: Use scraped data for platform features

## 🔮 **Future Enhancements**

### **Planned Features**
- **Urdu Language Support**: Scrape Urdu news sources
- **Image OCR**: Extract text from price images
- **Social Media Integration**: Monitor Twitter/Facebook for updates
- **Real-Time Alerts**: Push notifications for breaking news
- **AI Summarization**: Generate article summaries
- **Trend Analysis**: Predict price movements
- **User Subscriptions**: Personalized news feeds
- **API Rate Limiting**: Prevent abuse and ensure fair usage

This comprehensive news scraping system provides Drive On with a constant stream of relevant, categorized, and location-specific news about petrol prices, car prices, and the automobile industry across Pakistan! 🚀
