# Drive On Backend API Plan

## Project Overview
FastAPI backend for the Drive On platform - a comprehensive driver and job matching platform with community features.

## Technology Stack
- **Framework**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL (main data) + Firebase Firestore (real-time features)
- **Authentication**: Firebase Auth
- **File Storage**: Firebase Storage
- **Deployment**: Docker + Cloud Platform
- **Documentation**: Auto-generated OpenAPI/Swagger

## Firebase Integration
- **Project ID**: drive-on-b2af8
- **Services Used**:
  - Firebase Authentication (user management)
  - Firebase Storage (file uploads)
  - Firebase Firestore (real-time chat/notifications)
  - Firebase Cloud Messaging (push notifications)

## API Architecture

### Base URL Structure
```
/api/v1/
├── auth/          # Authentication endpoints
├── drivers/       # Driver management
├── jobs/          # Job portal
├── news/          # Industry news
├── forum/         # Community forum
├── uploads/       # File upload endpoints
└── admin/         # Admin panel endpoints
```

## Database Schema

### PostgreSQL Tables
1. **users** - Basic user information
2. **drivers** - Driver profiles and details
3. **jobs** - Job postings
4. **applications** - Job applications
5. **news_articles** - Industry news
6. **categories** - Job/news categories
7. **ratings** - Driver/employer ratings

### Firebase Firestore Collections
1. **chat_rooms** - Forum discussions
2. **messages** - Real-time messages
3. **notifications** - User notifications
4. **live_updates** - Real-time job updates

## API Endpoints Specification

### 1. Authentication (`/api/v1/auth/`)
```
POST   /register           # User registration
POST   /login              # User login
POST   /logout             # User logout
GET    /me                 # Get current user
PUT    /me                 # Update current user
POST   /verify-token       # Verify Firebase token
POST   /refresh-token      # Refresh authentication token
```

### 2. Drivers (`/api/v1/drivers/`)
```
GET    /                   # List all drivers (with filters)
POST   /                   # Create driver profile
GET    /{driver_id}        # Get specific driver
PUT    /{driver_id}        # Update driver profile
DELETE /{driver_id}        # Delete driver profile
POST   /{driver_id}/verify # Verify driver (admin only)
GET    /search             # Search drivers with advanced filters
POST   /request            # Request a driver
GET    /requests           # Get driver requests
```

### 3. Jobs (`/api/v1/jobs/`)
```
GET    /                   # List all jobs (with filters)
POST   /                   # Create job posting
GET    /{job_id}           # Get specific job
PUT    /{job_id}           # Update job posting
DELETE /{job_id}           # Delete job posting
POST   /{job_id}/apply     # Apply for job
GET    /{job_id}/applications # Get job applications
GET    /my-jobs            # Get user's posted jobs
GET    /my-applications    # Get user's applications
```

### 4. News (`/api/v1/news/`)
```
GET    /                   # List all news articles
POST   /                   # Create news article (admin only)
GET    /{article_id}       # Get specific article
PUT    /{article_id}       # Update article (admin only)
DELETE /{article_id}       # Delete article (admin only)
GET    /categories         # Get news categories
```

### 5. Forum (`/api/v1/forum/`)
```
GET    /rooms              # List chat rooms
POST   /rooms              # Create chat room
GET    /rooms/{room_id}    # Get chat room details
POST   /rooms/{room_id}/messages # Send message
GET    /rooms/{room_id}/messages # Get messages
POST   /voice-message      # Upload voice message
```

### 6. File Uploads (`/api/v1/uploads/`)
```
POST   /profile-image      # Upload profile image
POST   /document           # Upload driver documents
POST   /vehicle-image      # Upload vehicle images
POST   /voice-message      # Upload voice message
DELETE /file/{file_id}     # Delete uploaded file
```

### 7. Admin (`/api/v1/admin/`)
```
GET    /dashboard          # Admin dashboard stats
GET    /users              # Manage users
POST   /users/{user_id}/verify # Verify users
GET    /reports            # Get platform reports
POST   /news               # Create news articles
```

## Response Format
All API responses follow this wrapper format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

Error responses:
```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE",
  "details": {}
}
```

## Authentication Flow
1. Frontend sends Firebase ID token to backend
2. Backend verifies token with Firebase Admin SDK
3. Backend creates/updates user in PostgreSQL
4. Backend returns user data and permissions

## File Upload Flow
1. Frontend requests upload URL from backend
2. Backend generates signed Firebase Storage URL
3. Frontend uploads directly to Firebase Storage
4. Frontend notifies backend of successful upload
5. Backend saves file metadata to database

## Real-time Features
- **WebSocket connections** for live chat
- **Firebase Firestore listeners** for real-time updates
- **Push notifications** via Firebase Cloud Messaging

## Security Measures
- Firebase token validation on all protected routes
- Role-based access control (driver, employer, admin)
- File upload validation and virus scanning
- Rate limiting on API endpoints
- CORS configuration for frontend domain

## Development Phases

### Phase 1: Core Setup
- [x] Project structure
- [ ] FastAPI application setup
- [ ] Firebase integration
- [ ] Database models
- [ ] Authentication system

### Phase 2: Basic Features
- [ ] User management
- [ ] Driver profiles
- [ ] Job postings
- [ ] File uploads

### Phase 3: Advanced Features
- [ ] Search and filtering
- [ ] Real-time chat
- [ ] Notifications
- [ ] Admin panel

### Phase 4: Production Ready
- [ ] Testing suite
- [ ] Docker configuration
- [ ] CI/CD pipeline
- [ ] Performance optimization

## Environment Variables
```
# Database
DATABASE_URL=postgresql://user:password@localhost/driveon

# Firebase
FIREBASE_PROJECT_ID=drive-on-b2af8
FIREBASE_STORAGE_BUCKET=drive-on-b2af8.appspot.com

# Security
SECRET_KEY=your-secret-key
ALLOWED_ORIGINS=http://localhost:5174,https://yourdomain.com

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-key
```

## Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- Firebase emulator for testing
- Automated testing in CI/CD

## Deployment Architecture
```
Frontend (Vercel/Netlify) → API Gateway → FastAPI Backend → PostgreSQL
                                    ↓
                              Firebase Services
```

This plan ensures scalability, maintainability, and alignment with the frontend requirements.
