# Driver Registration System Update

## Overview
Updated the backend to implement a comprehensive driver registration system with all required fields, documents, and approval workflow as requested.

## Changes Made

### 1. Database Model Updates (`app/models/user.py`)

**New Required Fields Added:**
- `name` - Driver's full name (required)
- `father_name` - Father's full name (required) 
- `mobile_number` - Mobile number with country code (required)
- `education` - Education level (required)
- `experience_years` - Years of driving experience (required)
- `marital_status` - Marital status (required)
- `city_of_priority` - Preferred city to work in (required)

**New Document Fields:**
- `profile_picture_url` - Profile picture (optional)
- `cnic_front_url` - CNIC front image (required)
- `cnic_back_url` - CNIC back image (required)
- `driving_license_url` - Driving license image (required)
- `electricity_bill_url` - Electricity bill image (required)
- `police_certificate_url` - Police certificate image (required)

**New Status Fields:**
- `application_status` - Application review status (pending, under_review, approved, rejected)
- `documents_verified` - Whether all documents are verified
- `admin_notes` - Admin notes for application review
- `rejection_reason` - Reason for rejection if applicable
- `approved_at` - Timestamp when application was approved

### 2. Schema Updates (`app/schemas/user.py`)

**New Enums:**
- `ApplicationStatus` - Application status values
- `MaritalStatus` - Marital status options (single, married, divorced, widowed)
- `EducationLevel` - Education levels (primary, secondary, bachelor, etc.)

**New Schemas:**
- `DriverRegistrationRequest` - Complete registration request with validation
- `DriverApplicationResponse` - Response after application submission
- `PendingDriverApplication` - Schema for admin pending applications list
- `DriverDocumentUpload` - Document upload response schema

### 3. API Endpoints Updates (`app/api/v1/drivers.py`)

**New Registration Endpoint:**
```
POST /api/v1/drivers/register
```
- Accepts all required personal information and document URLs
- Automatically fetches email from Firebase authentication
- Validates all required documents are uploaded
- Creates application in "pending" status
- Returns application ID and estimated review time

**New Admin Endpoints:**
```
GET /api/v1/drivers/pending-applications
PUT /api/v1/drivers/applications/{application_id}/review
```
- Admin can view all pending applications
- Admin can approve, reject, or request changes
- Includes pagination and status filtering

**New User Endpoint:**
```
GET /api/v1/drivers/my-application
```
- Users can check their application status
- Shows admin notes and rejection reasons if applicable

### 4. Service Layer Updates (`app/services/user_service.py`)

**New Methods:**
- `create_driver_application()` - Create new application with all fields
- `get_pending_applications()` - Get applications for admin review
- `review_application()` - Admin review and status update
- `get_applications_by_status()` - Filter applications by status

### 5. File Upload Updates (`app/api/v1/uploads.py`)

**New Document Types:**
- `cnic_front` - CNIC front image
- `cnic_back` - CNIC back image  
- `driving_license` - Driving license image
- `electricity_bill` - Electricity bill image
- `police_certificate` - Police certificate image

## API Usage Examples

### 1. Driver Registration Flow

**Step 1: Upload Required Documents**
```bash
# Upload each required document
POST /api/v1/uploads/document
Content-Type: multipart/form-data

{
  "file": <image_file>,
  "document_type": "cnic_front"
}

# Response
{
  "success": true,
  "message": "CNIC front document uploaded successfully",
  "data": {
    "file_url": "https://storage.googleapis.com/...",
    "document_type": "cnic_front"
  }
}
```

**Step 2: Submit Registration Application**
```bash
POST /api/v1/drivers/register
Authorization: Bearer <firebase_token>
Content-Type: application/json

{
  "name": "Muhammad Ahmed",
  "father_name": "Muhammad Ali", 
  "mobile_number": "+923001234567",
  "education": "bachelor",
  "experience_years": 5,
  "marital_status": "married",
  "city_of_priority": "Karachi",
  "cnic_front_url": "https://storage.googleapis.com/...",
  "cnic_back_url": "https://storage.googleapis.com/...",
  "driving_license_url": "https://storage.googleapis.com/...",
  "electricity_bill_url": "https://storage.googleapis.com/...",
  "police_certificate_url": "https://storage.googleapis.com/...",
  "profile_picture_url": "https://storage.googleapis.com/...",
  "bio": "Experienced driver with clean record"
}

# Response
{
  "success": true,
  "message": "Driver application submitted successfully",
  "data": {
    "application_id": 123,
    "status": "pending",
    "message": "Your application will be reviewed within 3-5 business days",
    "submitted_at": "2024-01-15T10:30:00Z",
    "estimated_review_time": "3-5 business days"
  }
}
```

### 2. Check Application Status

```bash
GET /api/v1/drivers/my-application
Authorization: Bearer <firebase_token>

# Response
{
  "success": true,
  "message": "Application status retrieved successfully",
  "data": {
    "has_application": true,
    "application": {
      "id": 123,
      "status": "pending",
      "submitted_at": "2024-01-15T10:30:00Z",
      "admin_notes": null,
      "rejection_reason": null,
      "approved_at": null
    }
  }
}
```

### 3. Admin Review Applications

**Get Pending Applications:**
```bash
GET /api/v1/drivers/pending-applications?page=1&limit=20
Authorization: Bearer <admin_firebase_token>

# Response
{
  "success": true,
  "message": "Pending applications retrieved successfully",
  "data": {
    "applications": [
      {
        "id": 123,
        "name": "Muhammad Ahmed",
        "father_name": "Muhammad Ali",
        "mobile_number": "+923001234567",
        "education": "bachelor",
        "experience_years": 5,
        "marital_status": "married",
        "city_of_priority": "Karachi",
        "application_status": "pending",
        "submitted_at": "2024-01-15T10:30:00Z",
        "user_email": "<EMAIL>"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1
    }
  }
}
```

**Review Application:**
```bash
PUT /api/v1/drivers/applications/123/review
Authorization: Bearer <admin_firebase_token>
Content-Type: application/json

{
  "action": "approve",
  "admin_notes": "All documents verified. Driver approved for platform."
}

# Response
{
  "success": true,
  "message": "Application approved successfully",
  "data": {
    "application_id": 123,
    "action": "approve",
    "admin_notes": "All documents verified. Driver approved for platform."
  }
}
```

## Validation Rules

### Personal Information
- **Name**: 2-255 characters, required
- **Father Name**: 2-255 characters, required  
- **Mobile Number**: Valid international format with country code
- **Education**: Must be one of the predefined education levels
- **Experience Years**: 0-50 years, required
- **Marital Status**: Must be single, married, divorced, or widowed
- **City of Priority**: 2-100 characters, required

### Documents
- **All 5 documents are required** (CNIC front/back, driving license, electricity bill, police certificate)
- **Profile picture is optional**
- **File types**: JPEG, PNG, PDF
- **File size**: Maximum 10MB per file
- **Security**: Documents are stored in Firebase Storage with proper access controls

### Application Flow
1. **User uploads all required documents** using the upload endpoints
2. **User submits registration** with personal info and document URLs
3. **System validates** all required fields and documents are provided
4. **Application goes to "pending" status** for admin review
5. **Admin reviews and approves/rejects** the application
6. **User receives notification** of approval/rejection status

## Email Integration
- **Email is automatically fetched** from Firebase authentication token
- **No need to manually enter email** during registration
- **Email is used for notifications** and admin communication

## Security Features
- **Firebase authentication required** for all endpoints
- **Role-based access control** (admin-only endpoints)
- **Document validation** and file type checking
- **Secure file storage** in Firebase Storage
- **Input validation** and sanitization

## Database Migration
When deploying these changes, you'll need to run database migrations to add the new columns to the `drivers` table. The new fields are designed to be backward compatible with existing data.

## Frontend Integration
The frontend should:
1. **Collect all required personal information** in a multi-step form
2. **Upload documents first**, then use the returned URLs in registration
3. **Validate form completion** before enabling submit button
4. **Show application status** and admin feedback to users
5. **Handle different application states** (pending, approved, rejected)

This comprehensive update provides a complete driver registration and approval system that meets all the specified requirements.
