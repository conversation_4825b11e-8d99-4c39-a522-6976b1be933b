version: '3.8'

services:
  # FastAPI Backend
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*************************************/driveon
      - FIREBASE_PROJECT_ID=drive-on-b2af8
      - FIREBASE_STORAGE_BUCKET=drive-on-b2af8.appspot.com
      - FIREBASE_CREDENTIALS_PATH=/app/drive-on-b2af8-firebase-adminsdk-fbsvc-05c376e78b.json
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - ALLOWED_ORIGINS=http://localhost:5174,http://localhost:3000
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - .:/app
    depends_on:
      - db
    restart: unless-stopped

  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=driveon
      - POSTGRES_USER=driveon
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
