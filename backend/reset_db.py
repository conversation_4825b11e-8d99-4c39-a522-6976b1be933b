#!/usr/bin/env python3
"""
Database Reset Script for Drive On
Recreates the database with proper foreign key constraints
"""

import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.core.database import engine, Base
from app.models.user import User, Driver
from app.models.job import Job, JobApplication, Conversation
from app.models.news import NewsArticle
from app.models.forum import ForumMessage

def reset_database():
    """Reset the database with proper schema"""
    print("🔄 Resetting database...")
    
    # Drop all tables
    print("🗑️ Dropping existing tables...")
    Base.metadata.drop_all(bind=engine)
    
    # Create all tables with proper foreign keys
    print("🏗️ Creating tables with proper schema...")
    Base.metadata.create_all(bind=engine)
    
    print("✅ Database reset complete!")
    print("📊 All tables created with proper foreign key constraints")

if __name__ == "__main__":
    try:
        reset_database()
    except Exception as e:
        print(f"❌ Error resetting database: {e}")
        sys.exit(1)
