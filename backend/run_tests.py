#!/usr/bin/env python3
"""
Quick Backend Test Runner for Drive On API
Starts the server and runs basic tests
"""

import subprocess
import time
import sys
import os
import requests
import threading
from pathlib import Path

def start_server():
    """Start the FastAPI server"""
    try:
        print("🚀 Starting FastAPI server...")
        # Change to backend directory
        backend_dir = Path(__file__).parent
        os.chdir(backend_dir)
        
        # Start the server
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        return process
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return None

def wait_for_server(url="http://localhost:8000", timeout=30):
    """Wait for server to be ready"""
    print("⏳ Waiting for server to be ready...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Server is ready!")
                return True
        except requests.exceptions.RequestException:
            pass
        
        time.sleep(2)
    
    print("❌ Server failed to start within timeout")
    return False

def run_basic_tests():
    """Run basic API tests"""
    base_url = "http://localhost:8000"
    
    print("\n🧪 Running Basic API Tests...")
    print("=" * 50)
    
    tests = [
        ("Health Check", "GET", "/health", None, False),
        ("API Documentation", "GET", "/docs", None, False),
        ("Get Drivers", "GET", "/api/v1/drivers/", None, False),
        ("Get Jobs", "GET", "/api/v1/jobs/", None, False),
        ("Get News", "GET", "/api/v1/news/", None, False),
        ("Get Forums", "GET", "/api/v1/forum/", None, True),
        ("Driver Search", "GET", "/api/v1/drivers/search?q=test", None, False),
        ("Verify Token (Invalid)", "POST", "/api/v1/auth/verify-token", {"token": "invalid"}, False),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, method, endpoint, data, auth_required in tests:
        try:
            url = f"{base_url}{endpoint}"
            headers = {"Content-Type": "application/json"}
            
            if method == "GET":
                if "?" in endpoint:
                    response = requests.get(url, headers=headers, timeout=10)
                else:
                    response = requests.get(url, headers=headers, timeout=10)
            elif method == "POST":
                response = requests.post(url, json=data, headers=headers, timeout=10)
            
            # Consider various success conditions
            if response.status_code in [200, 201, 401, 422]:  # 401 for auth required, 422 for validation
                print(f"✅ {test_name} - Status: {response.status_code}")
                passed += 1
            else:
                print(f"❌ {test_name} - Status: {response.status_code}")
                if response.status_code >= 500:
                    print(f"   Error: {response.text[:100]}")
        
        except Exception as e:
            print(f"❌ {test_name} - Error: {str(e)[:100]}")
    
    print(f"\n📊 Test Results: {passed}/{total} passed ({(passed/total)*100:.1f}%)")
    return passed == total

def test_database_setup():
    """Test database setup"""
    print("\n🗄️ Testing Database Setup...")
    
    try:
        # Try to import and test database connection
        sys.path.append(os.path.dirname(__file__))
        from app.core.database import engine, get_db
        from sqlalchemy import text
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                print("✅ Database connection successful")
                return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_firebase_config():
    """Test Firebase configuration"""
    print("\n🔥 Testing Firebase Configuration...")
    
    try:
        from app.core.firebase import initialize_firebase
        from app.core.config import settings
        
        # Check if Firebase credentials file exists
        if os.path.exists(settings.FIREBASE_CREDENTIALS_PATH):
            print("✅ Firebase credentials file found")
            
            # Try to initialize Firebase
            try:
                initialize_firebase()
                print("✅ Firebase initialized successfully")
                return True
            except Exception as e:
                print(f"⚠️ Firebase initialization warning: {e}")
                return True  # Still consider it a pass if file exists
        else:
            print(f"❌ Firebase credentials file not found: {settings.FIREBASE_CREDENTIALS_PATH}")
            return False
    
    except Exception as e:
        print(f"❌ Firebase configuration error: {e}")
        return False

def check_dependencies():
    """Check if all dependencies are installed"""
    print("\n📦 Checking Dependencies...")
    
    required_packages = [
        "fastapi", "uvicorn", "sqlalchemy", "psycopg2-binary", 
        "firebase-admin", "python-multipart", "pillow", "requests"
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ Missing packages: {', '.join(missing)}")
        print("Install with: pip install " + " ".join(missing))
        return False
    
    return True

def main():
    """Main test runner"""
    print("🧪 Drive On Backend Test Runner")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first")
        return False
    
    # Test database setup
    db_ok = test_database_setup()
    
    # Test Firebase config
    firebase_ok = test_firebase_config()
    
    # Start server
    server_process = start_server()
    if not server_process:
        return False
    
    try:
        # Wait for server to be ready
        if not wait_for_server():
            return False
        
        # Run API tests
        api_tests_ok = run_basic_tests()
        
        # Print final summary
        print("\n" + "=" * 50)
        print("📋 FINAL SUMMARY")
        print("=" * 50)
        print(f"Database Setup: {'✅ OK' if db_ok else '❌ FAILED'}")
        print(f"Firebase Config: {'✅ OK' if firebase_ok else '❌ FAILED'}")
        print(f"API Tests: {'✅ OK' if api_tests_ok else '❌ FAILED'}")
        
        overall_success = db_ok and firebase_ok and api_tests_ok
        print(f"\nOverall Status: {'✅ ALL SYSTEMS GO' if overall_success else '❌ ISSUES DETECTED'}")
        
        if not overall_success:
            print("\n🔧 Recommendations:")
            if not db_ok:
                print("   - Check database configuration in .env file")
                print("   - Ensure PostgreSQL is running (or using SQLite for development)")
            if not firebase_ok:
                print("   - Verify Firebase credentials file exists")
                print("   - Check Firebase project configuration")
            if not api_tests_ok:
                print("   - Check server logs for errors")
                print("   - Verify all endpoints are properly configured")
        
        return overall_success
    
    finally:
        # Clean up server process
        if server_process:
            print("\n🛑 Stopping server...")
            server_process.terminate()
            try:
                server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                server_process.kill()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
