# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/driveon

# Firebase Configuration
FIREBASE_PROJECT_ID=drive-on-b2af8
FIREBASE_STORAGE_BUCKET=drive-on-b2af8.appspot.com
FIREBASE_CREDENTIALS_PATH=./drive-on-b2af8-firebase-adminsdk-fbsvc-05c376e78b.json

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=http://localhost:5174,http://localhost:3000,https://yourdomain.com

# Environment
ENVIRONMENT=development
DEBUG=True

# External APIs
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# File Upload Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
