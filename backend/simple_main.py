"""
Simple Drive On Backend API for Testing
FastAPI application without Firebase dependencies
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional
import uvicorn

# Simple data models
class DriverCreate(BaseModel):
    name: str
    mobile_number: str
    education: str
    experience_years: int

class JobCreate(BaseModel):
    title: str
    description: str
    location: str

class TokenVerify(BaseModel):
    token: str

# Create FastAPI application
app = FastAPI(
    title="Drive On API (Simple)",
    description="Simple backend API for testing Drive On platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global exception handler
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}"
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error",
            "error_code": "INTERNAL_ERROR"
        }
    )

# Health check endpoints
@app.get("/")
async def root():
    return {
        "success": True,
        "message": "Drive On API is running",
        "data": {
            "version": "1.0.0",
            "status": "healthy"
        }
    }

@app.get("/health")
async def health_check():
    return {
        "success": True,
        "message": "API is healthy",
        "data": {
            "status": "ok",
            "version": "1.0.0"
        }
    }

# Authentication endpoints
@app.post("/api/v1/auth/verify-token")
async def verify_token(token_data: TokenVerify):
    # Simulate token verification
    if token_data.token == "valid_token":
        return {
            "success": True,
            "message": "Token is valid",
            "data": {"user_id": "test_user"}
        }
    else:
        raise HTTPException(status_code=401, detail="Invalid token")

@app.get("/api/v1/auth/me")
async def get_current_user():
    # Simulate authentication required
    raise HTTPException(status_code=401, detail="Authentication required")

# Driver endpoints
@app.get("/api/v1/drivers/")
async def get_drivers():
    return {
        "success": True,
        "message": "Drivers retrieved successfully",
        "data": [
            {
                "id": 1,
                "name": "John Doe",
                "mobile_number": "+923001234567",
                "education": "bachelors",
                "experience_years": 5,
                "status": "active"
            },
            {
                "id": 2,
                "name": "Jane Smith",
                "mobile_number": "+923007654321",
                "education": "masters",
                "experience_years": 8,
                "status": "active"
            }
        ]
    }

@app.get("/api/v1/drivers/search")
async def search_drivers(q: str = ""):
    return {
        "success": True,
        "message": "Driver search completed",
        "data": [
            {
                "id": 1,
                "name": "John Doe",
                "mobile_number": "+923001234567",
                "education": "bachelors",
                "experience_years": 5
            }
        ]
    }

@app.get("/api/v1/drivers/{driver_id}")
async def get_driver(driver_id: int):
    if driver_id == 1:
        return {
            "success": True,
            "message": "Driver retrieved successfully",
            "data": {
                "id": 1,
                "name": "John Doe",
                "mobile_number": "+923001234567",
                "education": "bachelors",
                "experience_years": 5,
                "status": "active"
            }
        }
    else:
        raise HTTPException(status_code=404, detail="Driver not found")

@app.post("/api/v1/drivers/register")
async def register_driver(driver: DriverCreate):
    # Simulate authentication required
    raise HTTPException(status_code=401, detail="Authentication required")

@app.get("/api/v1/drivers/pending-applications")
async def get_pending_applications():
    # Simulate admin endpoint
    raise HTTPException(status_code=403, detail="Admin access required")

# Job endpoints
@app.get("/api/v1/jobs/")
async def get_jobs():
    return {
        "success": True,
        "message": "Jobs retrieved successfully",
        "data": [
            {
                "id": 1,
                "title": "Personal Driver Needed",
                "description": "Looking for a reliable driver",
                "location": "Karachi",
                "salary_min": 30000,
                "salary_max": 50000,
                "status": "active"
            },
            {
                "id": 2,
                "title": "Company Driver Position",
                "description": "Full-time driver for company",
                "location": "Lahore",
                "salary_min": 40000,
                "salary_max": 60000,
                "status": "active"
            }
        ]
    }

@app.get("/api/v1/jobs/{job_id}")
async def get_job(job_id: int):
    if job_id == 1:
        return {
            "success": True,
            "message": "Job retrieved successfully",
            "data": {
                "id": 1,
                "title": "Personal Driver Needed",
                "description": "Looking for a reliable driver",
                "location": "Karachi",
                "salary_min": 30000,
                "salary_max": 50000,
                "status": "active"
            }
        }
    else:
        raise HTTPException(status_code=404, detail="Job not found")

@app.post("/api/v1/jobs/")
async def create_job(job: JobCreate):
    # Simulate authentication required
    raise HTTPException(status_code=401, detail="Authentication required")

# Application endpoints
@app.post("/api/v1/applications/")
async def create_application():
    raise HTTPException(status_code=401, detail="Authentication required")

@app.get("/api/v1/applications/my-applications")
async def get_my_applications():
    raise HTTPException(status_code=401, detail="Authentication required")

# News endpoints
@app.get("/api/v1/news/")
async def get_news():
    return {
        "success": True,
        "message": "News retrieved successfully",
        "data": [
            {
                "id": 1,
                "title": "New Traffic Rules in Karachi",
                "content": "Latest traffic regulations...",
                "category": "traffic",
                "published_at": "2024-01-01T00:00:00Z"
            }
        ]
    }

@app.get("/api/v1/news/{news_id}")
async def get_news_item(news_id: int):
    if news_id == 1:
        return {
            "success": True,
            "message": "News item retrieved successfully",
            "data": {
                "id": 1,
                "title": "New Traffic Rules in Karachi",
                "content": "Latest traffic regulations...",
                "category": "traffic",
                "published_at": "2024-01-01T00:00:00Z"
            }
        }
    else:
        raise HTTPException(status_code=404, detail="News item not found")

@app.get("/api/v1/news/categories")
async def get_news_categories():
    return {
        "success": True,
        "message": "News categories retrieved successfully",
        "data": ["traffic", "jobs", "general", "announcements"]
    }

# Forum endpoints
@app.get("/api/v1/forum/")
async def get_forums():
    return {
        "success": True,
        "message": "Forums retrieved successfully",
        "data": [
            {
                "id": 1,
                "name": "General Discussion",
                "description": "General chat for drivers",
                "message_count": 150
            }
        ]
    }

@app.get("/api/v1/forum/{forum_id}/messages")
async def get_forum_messages(forum_id: int):
    raise HTTPException(status_code=401, detail="Authentication required")

@app.post("/api/v1/forum/{forum_id}/messages")
async def send_forum_message(forum_id: int):
    raise HTTPException(status_code=401, detail="Authentication required")

# Messaging endpoints
@app.get("/api/v1/messaging/conversations")
async def get_conversations():
    raise HTTPException(status_code=401, detail="Authentication required")

@app.post("/api/v1/messaging/contact-driver")
async def contact_driver():
    raise HTTPException(status_code=401, detail="Authentication required")

# Upload endpoints
@app.post("/api/v1/uploads/profile-image")
async def upload_profile_image():
    raise HTTPException(status_code=401, detail="Authentication required")

@app.post("/api/v1/uploads/document")
async def upload_document():
    raise HTTPException(status_code=401, detail="Authentication required")

# Notification endpoints
@app.get("/api/v1/notifications/")
async def get_notifications():
    raise HTTPException(status_code=401, detail="Authentication required")

# Admin endpoints
@app.get("/api/v1/admin/dashboard")
async def admin_dashboard():
    raise HTTPException(status_code=403, detail="Admin access required")

if __name__ == "__main__":
    uvicorn.run(
        "simple_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
