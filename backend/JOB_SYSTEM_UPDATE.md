# Job Posting & Private Messaging System Update

## Overview
Updated the backend to implement a comprehensive job posting system with all required fields and a private messaging system for communication between drivers and job posters.

## Changes Made

### 1. Database Model Updates (`app/models/job.py`)

**New Required Job Fields:**
- `job_category` - Type of job (household, big_vehicles, online_service, company)
- `package_amount` - Package amount in PKR (required)
- `currency` - Currency (defaults to PKR)
- `employment_type` - Part-time or full-time (required)

**Benefits Fields (Optional - can select multiple or none):**
- `residency_provided` - Residency accommodation provided
- `food_provided` - Food provision included
- `medical_insurance` - Medical insurance coverage
- `other_benefits` - Other benefits description

**Contact Information:**
- `city` - City where job is located (required)
- `contact_mobile` - Contact mobile number (required)
- `contact_email` - Auto-fetched from Firebase authentication

**New Messaging Models:**
- `JobMessage` - Direct messages between users about jobs
- `Conversation` - Conversation threads between drivers and employers
- `ConversationMessage` - Individual messages within conversations

### 2. Schema Updates (`app/schemas/job.py`)

**New Enums:**
- `JobCategory` - Job type options (household, big_vehicles, online_service, company)
- `EmploymentType` - Employment type (part_time, full_time)
- `MessageType` - Message types (text, driver_profile_link, job_link, system)

**New Job Schemas:**
- `JobPostingRequest` - Complete job posting with all required fields
- `JobBenefits` - Benefits selection schema
- `JobUpdateRequest` - Job update schema
- `JobResponse` - Full job response with all details
- `JobListResponse` - Simplified job list response
- `JobSearchParams` - Advanced job search parameters

**New Messaging Schemas:**
- `SendMessageRequest` - Send message request
- `MessageResponse` - Message response
- `ConversationResponse` - Conversation details
- `ContactJobPosterRequest` - Driver contacts job poster
- `ContactDriverRequest` - Job poster contacts driver

### 3. Service Layer (`app/services/job_service.py`)

**JobService Methods:**
- `create_job()` - Create job with all new fields
- `search_jobs()` - Advanced job search with filters
- `update_job()` - Update job postings
- `increment_view_count()` - Track job views
- `increment_contact_count()` - Track job contacts
- `get_job_statistics()` - Get employer statistics

**MessagingService Methods:**
- `start_conversation()` - Start new conversation
- `send_message()` - Send message in conversation
- `get_user_conversations()` - Get user's conversations
- `get_conversation_messages()` - Get messages from conversation
- `mark_messages_as_read()` - Mark messages as read
- `get_unread_count()` - Get total unread count

### 4. API Endpoints Updates

**Enhanced Job Endpoints (`/api/v1/jobs/`):**
- `POST /` - Create job with all required fields
- `GET /` - Search jobs with advanced filters
- `GET /{job_id}` - Get job details (increments view count)
- `PUT /{job_id}` - Update job (owner only)
- `DELETE /{job_id}` - Delete job (owner only)
- `GET /my/posted` - Get user's posted jobs with statistics
- `POST /{job_id}/contact` - Driver contacts job poster

**New Messaging Endpoints (`/api/v1/messaging/`):**
- `GET /conversations` - Get user's conversations
- `GET /conversations/{id}/messages` - Get conversation messages
- `POST /conversations/{id}/messages` - Send message
- `POST /contact-driver` - Job poster contacts driver
- `GET /unread-count` - Get unread messages count
- `PUT /conversations/{id}/mark-read` - Mark conversation as read

## API Usage Examples

### 1. Job Posting Flow

**Create Job Post:**
```bash
POST /api/v1/jobs/
Authorization: Bearer <firebase_token>
Content-Type: application/json

{
  "title": "Personal Driver for Family",
  "description": "Looking for reliable driver for family transportation",
  "company_name": "Ahmed Family",
  "job_category": "household",
  "package_amount": 45000.0,
  "employment_type": "full_time",
  "benefits": {
    "residency_provided": true,
    "food_provided": true,
    "medical_insurance": false,
    "other_benefits": "Paid holidays and bonus"
  },
  "city": "Karachi",
  "contact_mobile": "+923001234567",
  "requirements": "Clean driving license, 3+ years experience",
  "vehicle_provided": true,
  "license_required": true,
  "experience_required": "mid"
}

# Response
{
  "success": true,
  "message": "Job posted successfully",
  "data": {
    "job_id": 123,
    "title": "Personal Driver for Family",
    "job_category": "household",
    "package_amount": 45000.0,
    "employment_type": "full_time",
    "city": "Karachi",
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

**Search Jobs:**
```bash
GET /api/v1/jobs/?job_category=household&employment_type=full_time&city=Karachi&min_package=30000&max_package=60000

# Response
{
  "success": true,
  "message": "Jobs retrieved successfully",
  "data": {
    "jobs": [
      {
        "id": 123,
        "title": "Personal Driver for Family",
        "job_category": "household",
        "package_amount": 45000.0,
        "employment_type": "full_time",
        "city": "Karachi",
        "company_name": "Ahmed Family",
        "residency_provided": true,
        "food_provided": true,
        "medical_insurance": false,
        "view_count": 25,
        "contact_count": 5,
        "created_at": "2024-01-15T10:30:00Z",
        "employer_name": "Ahmed Ali",
        "employer_verified": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1
    }
  }
}
```

### 2. Private Messaging Flow

**Driver Contacts Job Poster:**
```bash
POST /api/v1/jobs/123/contact
Authorization: Bearer <driver_firebase_token>
Content-Type: application/json

{
  "job_id": 123,
  "message": "Hello, I am interested in this driver position. I have 5 years of experience and clean driving record.",
  "include_driver_profile": true
}

# Response
{
  "success": true,
  "message": "Message sent to job poster successfully",
  "data": {
    "conversation_id": 456,
    "job_title": "Personal Driver for Family",
    "message_sent": true
  }
}
```

**Job Poster Contacts Driver:**
```bash
POST /api/v1/messaging/contact-driver
Authorization: Bearer <employer_firebase_token>
Content-Type: application/json

{
  "driver_id": 789,
  "job_id": 123,
  "message": "Hi, I saw your profile and I'm interested in discussing a driver position with you.",
  "include_job_details": true
}

# Response
{
  "success": true,
  "message": "Message sent to driver successfully",
  "data": {
    "conversation_id": 456,
    "driver_name": "Muhammad Ahmed",
    "job_title": "Personal Driver for Family",
    "message_sent": true
  }
}
```

**Get Conversations:**
```bash
GET /api/v1/messaging/conversations
Authorization: Bearer <firebase_token>

# Response
{
  "success": true,
  "message": "Conversations retrieved successfully",
  "data": {
    "conversations": [
      {
        "id": 456,
        "job_id": 123,
        "job_title": "Personal Driver for Family",
        "driver_name": "Muhammad Ahmed",
        "employer_name": "Ahmed Ali",
        "other_party_name": "Ahmed Ali",
        "status": "active",
        "last_message_at": "2024-01-15T11:00:00Z",
        "unread_count": 2,
        "total_messages": 5,
        "last_message": "Thank you for your interest. When can we...",
        "created_at": "2024-01-15T10:45:00Z"
      }
    ],
    "total_unread": 2,
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1
    }
  }
}
```

**Get Conversation Messages:**
```bash
GET /api/v1/messaging/conversations/456/messages
Authorization: Bearer <firebase_token>

# Response
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "messages": [
      {
        "id": 1,
        "message_text": "Hello, I am interested in this driver position.",
        "message_type": "text",
        "sender_name": "Muhammad Ahmed",
        "sender_id": 789,
        "is_read": true,
        "created_at": "2024-01-15T10:45:00Z",
        "read_at": "2024-01-15T10:50:00Z",
        "referenced_driver_name": null,
        "referenced_job_title": null
      },
      {
        "id": 2,
        "message_text": "Driver Profile",
        "message_type": "driver_profile_link",
        "sender_name": "Muhammad Ahmed",
        "sender_id": 789,
        "is_read": true,
        "created_at": "2024-01-15T10:45:00Z",
        "read_at": "2024-01-15T10:50:00Z",
        "referenced_driver_name": "Muhammad Ahmed",
        "referenced_job_title": null
      }
    ],
    "conversation_id": 456,
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 2
    }
  }
}
```

## Key Features Implemented

### 1. **Complete Job Posting System**
- ✅ All required fields (job type, package, benefits, contact info)
- ✅ Email auto-fetched from Firebase authentication
- ✅ Mobile number required input
- ✅ Flexible benefits selection (can select all, none, or any combination)
- ✅ Advanced job search and filtering
- ✅ Job statistics and analytics

### 2. **Private Messaging System**
- ✅ Drivers can contact job posters about specific jobs
- ✅ Job posters can contact drivers about opportunities
- ✅ Both parties can share profile/job links in messages
- ✅ Conversation threads organized by job
- ✅ Read/unread message tracking
- ✅ Message history and pagination
- ✅ Privacy controls (users can only access their own conversations)

### 3. **Contact & Communication Features**
- ✅ Contact counting for job analytics
- ✅ View counting for job popularity
- ✅ Unread message notifications
- ✅ Conversation management
- ✅ Message type support (text, profile links, job links)

### 4. **Security & Privacy**
- ✅ Users can only contact about jobs they don't own
- ✅ Conversation access restricted to participants
- ✅ Firebase authentication required for all messaging
- ✅ Input validation and sanitization
- ✅ Rate limiting and spam prevention

## Database Schema

### Job Table Updates
```sql
-- New required fields
job_category VARCHAR(50) NOT NULL
package_amount FLOAT NOT NULL
employment_type VARCHAR(20) NOT NULL

-- Benefits fields
residency_provided BOOLEAN DEFAULT FALSE
food_provided BOOLEAN DEFAULT FALSE
medical_insurance BOOLEAN DEFAULT FALSE
other_benefits TEXT

-- Contact fields
city VARCHAR(100) NOT NULL
contact_mobile VARCHAR(20) NOT NULL
contact_email VARCHAR(255) NOT NULL

-- Statistics
contact_count INTEGER DEFAULT 0
```

### New Messaging Tables
```sql
-- Conversations table
CREATE TABLE conversations (
    id SERIAL PRIMARY KEY,
    job_id INTEGER REFERENCES jobs(id),
    driver_id INTEGER REFERENCES users(id),
    employer_id INTEGER REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'active',
    last_message_at TIMESTAMP,
    total_messages INTEGER DEFAULT 0,
    unread_by_driver INTEGER DEFAULT 0,
    unread_by_employer INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Conversation messages table
CREATE TABLE conversation_messages (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER REFERENCES conversations(id),
    sender_id INTEGER REFERENCES users(id),
    message_text TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text',
    referenced_driver_id INTEGER REFERENCES drivers(id),
    referenced_job_id INTEGER REFERENCES jobs(id),
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Frontend Integration Guide

### 1. **Job Posting Form**
The frontend should collect:
- Job category (dropdown: household, big_vehicles, online_service, company)
- Package amount in PKR (number input)
- Employment type (radio: part_time, full_time)
- Benefits checkboxes (residency, food, medical insurance)
- City (text input)
- Mobile number (text input with validation)
- Email (auto-fetched, display only)

### 2. **Job Search & Filtering**
Support filters for:
- Job category
- Employment type
- City/location
- Package range (min/max)
- Benefits required
- Vehicle provided

### 3. **Private Messaging Interface**
Implement:
- Conversation list with unread indicators
- Message thread view with sender identification
- Send message form with option to include profile/job links
- Real-time message updates (polling or WebSocket)
- Message type indicators (text, profile link, job link)

### 4. **Contact Features**
Add buttons for:
- "Contact Job Poster" on job details page (for drivers)
- "Contact Driver" on driver profile page (for job posters)
- Message composer with profile/job link options
- Conversation management interface

This comprehensive update provides a complete job posting and private messaging system that handles all the requirements you specified, with proper security, privacy, and user experience considerations.
