# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Firebase
firebase-admin==6.4.0
google-cloud-firestore==2.13.1
google-cloud-storage==2.10.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8

# HTTP requests
httpx==0.25.2
requests==2.31.0

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# CORS
fastapi-cors==0.0.6

# File handling
pillow==10.1.0
python-magic==0.4.27

# Utilities
python-dateutil==2.8.2
pytz==2023.3

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# WebSocket support
websockets==12.0

# Web scraping dependencies
aiohttp==3.9.1
beautifulsoup4==4.12.2
feedparser==6.0.10
schedule==1.2.0
lxml==4.9.3
html5lib==1.1
