"""
Initialize default forums and cities for Drive On platform
"""

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.forum import Forum, City
from app.services.forum_service import CityService


def create_default_forums(db: Session):
    """Create default forums for the platform"""
    
    # Get major cities
    city_service = CityService(db)
    city_service.create_default_cities()
    cities = city_service.get_major_cities()
    
    # Default forums
    default_forums = [
        {
            "name": "General Traffic Updates",
            "slug": "general-traffic-updates",
            "description": "General traffic updates and road conditions across Pakistan",
            "icon": "🚦",
            "color": "#FF6B35",
            "forum_type": "traffic_updates",
            "city_filter": None,
            "allow_voice_messages": True,
            "allow_reactions": True,
            "allow_tagging": True
        },
        {
            "name": "Petrol Prices Updates",
            "slug": "petrol-prices-updates",
            "description": "Latest petrol and fuel price updates across Pakistan",
            "icon": "⛽",
            "color": "#4ECDC4",
            "forum_type": "petrol_prices",
            "city_filter": None,
            "allow_voice_messages": True,
            "allow_reactions": True,
            "allow_tagging": True
        },
        {
            "name": "Car Prices Discussion",
            "slug": "car-prices-discussion",
            "description": "Car prices, market trends, and vehicle discussions",
            "icon": "🚗",
            "color": "#45B7D1",
            "forum_type": "car_prices",
            "city_filter": None,
            "allow_voice_messages": True,
            "allow_reactions": True,
            "allow_tagging": True
        }
    ]
    
    # Create general forums
    for forum_data in default_forums:
        existing_forum = db.query(Forum).filter(Forum.slug == forum_data["slug"]).first()
        if not existing_forum:
            forum = Forum(**forum_data)
            db.add(forum)
            print(f"Created forum: {forum_data['name']}")
    
    # Create city-specific traffic forums for major cities
    for city in cities:
        city_forum_data = {
            "name": f"{city.name} Traffic Updates",
            "slug": f"{city.slug}-traffic-updates",
            "description": f"Traffic updates and road conditions in {city.name}, {city.province}",
            "icon": "🚦",
            "color": "#FF6B35",
            "forum_type": "traffic_updates",
            "city_filter": city.name,
            "allow_voice_messages": True,
            "allow_reactions": True,
            "allow_tagging": True
        }
        
        existing_city_forum = db.query(Forum).filter(Forum.slug == city_forum_data["slug"]).first()
        if not existing_city_forum:
            city_forum = Forum(**city_forum_data)
            db.add(city_forum)
            print(f"Created city forum: {city_forum_data['name']}")
    
    db.commit()
    print("✅ Default forums created successfully!")


def main():
    """Main function to initialize forums"""
    print("🚀 Initializing Drive On Forums...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Create default forums and cities
        create_default_forums(db)
        
        print("\n📊 Forum Summary:")
        forums = db.query(Forum).all()
        for forum in forums:
            print(f"  - {forum.name} ({forum.forum_type})")
            if forum.city_filter:
                print(f"    📍 City: {forum.city_filter}")
        
        print(f"\n🎉 Total forums created: {len(forums)}")
        
        print("\n🏙️ Cities Summary:")
        cities = db.query(City).filter(City.is_major_city == True).all()
        for city in cities:
            print(f"  - {city.name}, {city.province}")
        
        print(f"\n🎉 Total major cities: {len(cities)}")
        
    except Exception as e:
        print(f"❌ Error initializing forums: {str(e)}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    main()
