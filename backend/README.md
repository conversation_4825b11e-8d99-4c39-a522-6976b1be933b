# Drive On Backend API

FastAPI backend for the Drive On platform - connecting drivers with opportunities.

## Features

- 🔐 Firebase Authentication integration
- 📁 Firebase Storage for file uploads
- 🗄️ PostgreSQL database with SQLAlchemy ORM
- 📝 Auto-generated API documentation (Swagger/OpenAPI)
- 🔍 Advanced search and filtering
- 📱 Real-time features with Firebase Firestore
- 🐳 Docker containerization
- 🧪 Comprehensive testing suite

## Quick Start

### Prerequisites

- Python 3.11+
- Docker & Docker Compose (optional)
- PostgreSQL (if not using Docker)
- Firebase project with service account key

### Installation

1. **Clone and navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp env.example .env
   # Edit .env with your configuration
   ```

4. **Run with Docker (Recommended)**
   ```bash
   docker-compose up -d
   ```

   **Or run locally**
   ```bash
   # Start PostgreSQL database first
   python main.py
   ```

5. **Access the API**
   - API: http://localhost:8000
   - Documentation: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc

## API Endpoints

### Authentication (`/api/v1/auth/`)
- `POST /register` - Register new user
- `POST /login` - User login
- `POST /verify-token` - Verify Firebase token
- `GET /me` - Get current user info
- `PUT /me` - Update current user

### Drivers (`/api/v1/drivers/`)
- `GET /` - List drivers with filters
- `POST /` - Create driver profile
- `GET /{driver_id}` - Get specific driver
- `PUT /{driver_id}` - Update driver profile
- `GET /search` - Search drivers
- `POST /{driver_id}/verify` - Verify driver (admin)

### Jobs (`/api/v1/jobs/`)
- `GET /` - List jobs with filters
- `POST /` - Create job posting
- `GET /{job_id}` - Get specific job
- `PUT /{job_id}` - Update job
- `POST /{job_id}/apply` - Apply for job

### News (`/api/v1/news/`)
- `GET /` - List news articles
- `GET /categories` - Get news categories
- `GET /{article_id}` - Get specific article
- `POST /` - Create article (admin)

### Forum (`/api/v1/forum/`)
- `GET /rooms` - List chat rooms
- `POST /rooms` - Create chat room
- `GET /rooms/{room_id}/messages` - Get messages
- `POST /rooms/{room_id}/messages` - Send message

### File Uploads (`/api/v1/uploads/`)
- `POST /profile-image` - Upload profile image
- `POST /document` - Upload driver documents
- `POST /vehicle-image` - Upload vehicle image
- `POST /voice-message` - Upload voice message
- `DELETE /file/{filename}` - Delete file

### Admin (`/api/v1/admin/`)
- `GET /dashboard` - Admin dashboard stats
- `GET /users` - Manage users
- `POST /users/{user_id}/verify` - Verify users
- `GET /reports` - Platform reports

## Database Schema

### Core Tables
- `users` - User accounts and basic info
- `drivers` - Driver profiles and details
- `jobs` - Job postings
- `job_applications` - Job applications
- `news_articles` - Industry news
- `ratings` - User ratings and reviews

### Firebase Collections
- `chat_rooms` - Forum discussions
- `messages` - Real-time messages
- `notifications` - User notifications

## Configuration

### Environment Variables

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/driveon

# Firebase
FIREBASE_PROJECT_ID=drive-on-b2af8
FIREBASE_STORAGE_BUCKET=drive-on-b2af8.appspot.com
FIREBASE_CREDENTIALS_PATH=./drive-on-b2af8-firebase-adminsdk-fbsvc-05c376e78b.json

# Security
SECRET_KEY=your-super-secret-key
ALLOWED_ORIGINS=http://localhost:5174,https://yourdomain.com

# Features
MAX_FILE_SIZE=********  # 10MB
RATE_LIMIT_PER_MINUTE=60
```

## Development

### Running Tests
```bash
pytest
```

### Code Formatting
```bash
black .
flake8 .
```

### Database Migrations
```bash
# Generate migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head
```

## Deployment

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Manual Deployment
1. Set up PostgreSQL database
2. Configure environment variables
3. Run database migrations
4. Start the application with a production ASGI server

## API Response Format

All endpoints return responses in this format:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

Error responses:
```json
{
  "success": false,
  "message": "Error description",
  "error_code": "ERROR_CODE"
}
```

## Security Features

- Firebase token validation on protected routes
- Role-based access control
- File upload validation and size limits
- Rate limiting on API endpoints
- CORS configuration
- SQL injection prevention with SQLAlchemy ORM

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License.
