"""
Initialize news scraper with default Pakistani news sources and categories
"""

import asyncio
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.news_scraper import NewsScraperService
from app.services.scheduler import start_news_scheduler


async def initialize_news_system():
    """Initialize the complete news scraping system"""
    print("🚀 Initializing Drive On News Scraping System...")
    
    # Get database session
    db = next(get_db())
    
    try:
        # Initialize scraper service
        scraper_service = NewsScraperService(db)
        
        # Initialize default sources and categories
        print("\n📰 Setting up default news sources...")
        scraper_service.initialize_default_sources()
        
        print("\n📂 Setting up default categories...")
        scraper_service.initialize_default_categories()
        
        # Test scraping with one source
        print("\n🔍 Testing scraper with sample source...")
        async with scraper_service as scraper:
            # Get first active source for testing
            from app.models.news import NewsSource
            test_source = db.query(NewsSource).filter(
                NewsSource.is_active == True
            ).first()
            
            if test_source:
                print(f"Testing with: {test_source.name}")
                result = await scraper.scrape_source(test_source)
                
                if result["success"]:
                    print(f"✅ Test successful! Found {result['articles_created']} new articles")
                else:
                    print(f"❌ Test failed: {result.get('error', 'Unknown error')}")
            else:
                print("⚠️ No test source available")
        
        # Start the scheduler
        print("\n⏰ Starting automated scheduler...")
        start_news_scheduler()
        
        print("\n🎉 News scraping system initialized successfully!")
        print("\n📊 System Summary:")
        
        # Get statistics
        stats = scraper_service.get_scraping_stats()
        print(f"   📰 Total sources: {stats['total_sources']}")
        print(f"   ✅ Active sources: {stats['active_sources']}")
        print(f"   📄 Total articles: {stats['total_scraped_articles']}")
        print(f"   📅 Articles today: {stats['articles_today']}")
        
        # List all sources
        print(f"\n📋 Configured News Sources:")
        from app.models.news import NewsSource
        sources = db.query(NewsSource).filter(NewsSource.is_active == True).all()
        for i, source in enumerate(sources, 1):
            print(f"   {i}. {source.name}")
            print(f"      🌐 {source.base_url}")
            print(f"      📡 RSS: {'Yes' if source.rss_url else 'No'}")
            print(f"      🔄 Frequency: Every {source.scraping_frequency} hours")
            print(f"      📂 Categories: {source.categories}")
        
        # List all categories
        print(f"\n📂 News Categories:")
        from app.models.news import NewsCategory
        categories = db.query(NewsCategory).filter(NewsCategory.is_active == True).all()
        for i, category in enumerate(categories, 1):
            print(f"   {i}. {category.icon} {category.name}")
            print(f"      🔗 Slug: {category.slug}")
            print(f"      🎯 Priority: {category.priority}")
            print(f"      🔑 Keywords: {len(category.keywords) if category.keywords else 0}")
        
        print(f"\n⚡ Next Actions:")
        print(f"   • Scraping runs automatically every 2 hours")
        print(f"   • Manual scraping: POST /api/v1/news/scrape")
        print(f"   • View articles: GET /api/v1/news/")
        print(f"   • View statistics: GET /api/v1/news/stats")
        
    except Exception as e:
        print(f"❌ Initialization failed: {str(e)}")
        db.rollback()
    finally:
        db.close()


def main():
    """Main function"""
    asyncio.run(initialize_news_system())


if __name__ == "__main__":
    main()
