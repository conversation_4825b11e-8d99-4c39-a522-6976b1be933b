#!/usr/bin/env python3
"""
Development startup script for Drive On Backend
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Start the FastAPI development server"""
    
    # Check if we're in the backend directory
    if not Path("main.py").exists():
        print("❌ Error: main.py not found. Make sure you're in the backend directory.")
        sys.exit(1)
    
    # Check if Firebase credentials exist
    firebase_creds = "drive-on-b2af8-firebase-adminsdk-fbsvc-05c376e78b.json"
    if not Path(firebase_creds).exists():
        print(f"⚠️  Warning: Firebase credentials file '{firebase_creds}' not found.")
        print("   Some features may not work properly.")
    
    # Set environment variables for development
    os.environ.setdefault("ENVIRONMENT", "development")
    os.environ.setdefault("DEBUG", "true")
    os.environ.setdefault("DATABASE_URL", "sqlite:///./driveon.db")
    
    print("🚀 Starting Drive On Backend API...")
    print("📍 API will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔄 Auto-reload enabled for development")
    print("\n" + "="*50)
    
    try:
        # Start the FastAPI server with uvicorn
        subprocess.run([
            "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)
    except FileNotFoundError:
        print("❌ Error: uvicorn not found. Please install dependencies:")
        print("   pip install -r requirements.txt")
        sys.exit(1)

if __name__ == "__main__":
    main()
